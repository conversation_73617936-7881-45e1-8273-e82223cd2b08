#!/usr/bin/env python3
"""
Example script showing how to use the configuration system.

This demonstrates how to access configuration values throughout your application.
"""

from config.settings import Config

def main():
    """Demonstrate configuration usage."""
    
    print("=== Configuration Usage Examples ===\n")
    
    # Database configuration
    print("1. Database Configuration:")
    print(f"   Database URL: {Config.database.DATABASE_URL}")
    print(f"   Host: {Config.database.DB_HOST}")
    print(f"   Port: {Config.database.DB_PORT}")
    print(f"   Database: {Config.database.DB_NAME}")
    print(f"   User: {Config.database.DB_USER}")
    print(f"   Password: {'*' * len(Config.database.DB_PASSWORD)}")
    
    # Redis configuration
    print("\n2. Redis Configuration:")
    print(f"   Host: {Config.redis.REDIS_HOST}")
    print(f"   Port: {Config.redis.REDIS_PORT}")
    print(f"   Database: {Config.redis.REDIS_DB}")
    print(f"   URL: {Config.redis.REDIS_URL}")
    
    # API Keys
    print("\n3. API Keys:")
    print(f"   OpenAI: {'✓ Set' if Config.api_keys.OPENAI_API_KEY else '✗ Not set'}")
    print(f"   Tavily: {'✓ Set' if Config.api_keys.TAVILY_API_KEY else '✗ Not set'}")
    print(f"   Instagram: {'✓ Set' if Config.api_keys.INSTAGRAM_RAPID_API_KEY else '✗ Not set'}")
    print(f"   LinkedIn: {'✓ Set' if Config.api_keys.LINKEDIN_RAPID_API_KEY else '✗ Not set'}")
    print(f"   TikTok: {'✓ Set' if Config.api_keys.TIKTOK_RAPID_API_KEY else '✗ Not set'}")
    
    # Application settings
    print("\n4. Application Settings:")
    print(f"   Debug Mode: {Config.app.DEBUG}")
    print(f"   API Host: {Config.app.API_HOST}")
    print(f"   API Port: {Config.app.API_PORT}")
    print(f"   Log Level: {Config.app.LOG_LEVEL}")
    
    # Email configuration
    print("\n5. Email Configuration:")
    print(f"   SMTP Host: {Config.email.SMTP_HOST}")
    print(f"   SMTP Port: {Config.email.SMTP_PORT}")
    print(f"   SMTP User: {'✓ Set' if Config.email.SMTP_USER else '✗ Not set'}")
    print(f"   Admin Email: {'✓ Set' if Config.email.ADMIN_EMAIL else '✗ Not set'}")
    
    # Validation
    print("\n6. Configuration Validation:")
    missing_settings = Config.validate_required_settings()
    if missing_settings:
        print(f"   ❌ Missing settings: {', '.join(missing_settings)}")
    else:
        print("   ✅ All required settings are present")
    
    # Print full summary
    print("\n7. Full Configuration Summary:")
    Config.print_config_summary()
    
    # Example of using different database URLs
    print("\n8. Database URL Examples:")
    print(f"   Local PostgreSQL: {Config.get_database_url()}")
    if Config.database.AWS_DB_HOST:
        print(f"   AWS RDS: {Config.get_database_url(use_aws=True)}")
    else:
        print("   AWS RDS: Not configured")

if __name__ == "__main__":
    main()
