from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.declarative import declarative_base

from sqlalchemy.orm import sessionmaker, Session
import logging
import sys
from pathlib import Path

# Add project root to path for config import
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration from settings
DATABASE_URL = Config.database.DATABASE_URL
DB_HOST = Config.database.DB_HOST
DB_PORT = Config.database.DB_PORT
DB_USER = Config.database.DB_USER
DB_PASSWORD = Config.database.DB_PASSWORD
DB_NAME = Config.database.DB_NAME

# Create engine with connection pooling and proper configuration
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=300,    # Recycle connections every 5 minutes
    pool_size=10,        # Number of connections to maintain
    max_overflow=20,     # Additional connections that can be created
    echo=False           # Set to True for SQL debugging
)


# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

def get_db() -> Session:
    """
    Dependency function to get database session.
    Use this in FastAPI endpoints and Dramatiq tasks.
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_session() -> Session:
    """
    Get a database session for use in Dramatiq tasks.
    Remember to close the session when done.
    """
    return SessionLocal()

def init_db():
    """Initialize the database by creating all tables."""
    try:
        logger.info("Initializing database...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

def test_db_connection():
    """Test database connection."""
    try:
        with engine.connect() as connection:
           

            connection.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

# Event listeners for connection management
@event.listens_for(engine, "connect")
def set_postgresql_settings(dbapi_connection, connection_record):
    """Set database-specific configurations on connect."""
    if 'postgresql' in str(engine.url):
        # PostgreSQL specific settings can be added here
        logger.debug("PostgreSQL connection established")
        pass