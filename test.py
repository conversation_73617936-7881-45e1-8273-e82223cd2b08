from apify_client import ApifyClient

# Initialize the ApifyClient with your API token
API_TOKEN = "**********************************************"
client = ApifyClient(f"{API_TOKEN}")

# Prepare the Actor input
run_input = { "telegram_groups": [
        "https://t.me/+I1U4f5PY3yw1ODJl"
    ] }

# Run the Actor and wait for it to finish
run = client.actor("8vxvc9BwwG34zvS5P").call(run_input=run_input)


# Fetch and print Actor results from the run's dataset (if there are any)
for item in client.dataset(run["defaultDatasetId"]).iterate_items():
    print(f"item: {item}")