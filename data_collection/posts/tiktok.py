import re
import os
import sys
import requests
from dotenv import load_dotenv

# allow absolute imports from your project root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_collection.posts.base_collector import BaseSocialMediaCollector

load_dotenv()

class TikTokCollector(BaseSocialMediaCollector):
    def __init__(self):
        """
        Initialize the TikTok collector with RapidAPI credentials.
        """
        # Use the provided API key and host
        self.api_key = os.getenv("TIKTOK_RAPID_API_KEY", "c5dab30c19mshe65df2c159b3ceap1c15ffjsnc8f624885e94")
        self.api_host = os.getenv("TIKTOK_RAPID_API_HOST", "tiktok-api23.p.rapidapi.com")
        self.base_url = f"https://{self.api_host}"
        self.headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.api_host,
        }

    def _make_request(self, path: str, params: dict) -> dict:
        """
        Make a request to the TikTok API.

        Args:
            path: API endpoint path
            params: Query parameters

        Returns:
            API response as a dictionary
        """
        url = f"{self.base_url}{path}"

        try:
            # Make the API request
            response = requests.get(url, headers=self.headers, params=params, timeout=15)

            # Raise an exception for 4XX and 5XX status codes
            response.raise_for_status()

            # Parse and return the JSON response
            return response.json()
        except Exception:
            return {}

    def get_profile_posts(self, url: str, count: int = 20) -> list[str]:
        """
        Fetches post URLs from a TikTok user's profile using the API.

        Args:
            url: TikTok URL or username
            count: Number of posts to retrieve

        Returns:
            List of TikTok post URLs
        """
        # Extract username if a URL is provided
        username = url
        if "tiktok.com" in url:
            try:
                username = self.extract_username_from_url(url)
                if __name__ == "__main__":
                    print(f"Extracted username from URL: {username}")
            except ValueError as e:
                # If we can't extract a username, just use the original value
                if __name__ == "__main__":
                    print(f"Could not extract username from URL: {e}")

                # Try a simpler approach - extract the part after tiktok.com/
                parts = url.split('tiktok.com/')
                if len(parts) > 1:
                    # Get everything after tiktok.com/
                    potential_username = parts[1]

                    # Remove @ if present
                    if potential_username.startswith('@'):
                        potential_username = potential_username[1:]

                    # Get the first part before any slash
                    if '/' in potential_username:
                        potential_username = potential_username.split('/')[0]

                    # If we have something, use it
                    if potential_username:
                        username = potential_username
                        if __name__ == "__main__":
                            print(f"Using alternative username extraction: {username}")

        # Use the user/videos endpoint which has been tested and works
        endpoints = [
            # The user/videos endpoint works reliably
            {
                "path": "/user/videos",
                "params": {"username": username, "count": str(count)}
            }
        ]

        for endpoint in endpoints:
            try:
                path = endpoint["path"]
                params = endpoint["params"]

                # Make the API request
                response = self._make_request(path, params)

                # Extract posts from the response
                posts = []
                if response:
                    if "data" in response:
                        posts = response.get("data", [])
                    elif isinstance(response, list):
                        posts = response
                    elif "videos" in response:
                        posts = response.get("videos", [])
                    elif "items" in response:
                        posts = response.get("items", [])

                # Extract post URLs from the response
                post_urls = []
                for post in posts:
                    # Get the video ID
                    video_id = None
                    username = username  # Use the extracted username

                    # Try different field names for video ID
                    for field in ["id", "video_id", "aweme_id", "item_id", "videoId"]:
                        if isinstance(post, dict) and field in post:
                            video_id = post[field]
                            break

                    # Try to extract username from the post if available
                    if isinstance(post, dict) and "author" in post:
                        author = post["author"]
                        if isinstance(author, dict):
                            for field in ["username", "unique_id", "uniqueId", "nickname"]:
                                if field in author:
                                    username = author[field]
                                    break

                    if video_id:
                        # Construct the post URL
                        post_url = self.construct_post_url(username, video_id)
                        post_urls.append(post_url)

                if post_urls:
                    return post_urls
            except Exception:
                continue

        # If all endpoints failed, return an empty list
        return []

    def get_company_posts(self, url: str, count: int = 20) -> list[str]:
        # alias for get_profile_posts
        return self.get_profile_posts(url, count)

    def extract_username_from_url(self, url: str) -> str:
        """
        Extract the username from a TikTok URL.
        Example: https://www.tiktok.com/@username/video/1234567890123456789
        """
        m = re.search(r'tiktok\.com/@([^/]+)', url)
        if not m:
            raise ValueError(f"Could not extract username from URL: {url}")
        return m.group(1)

    def extract_video_id_from_url(self, url: str) -> str:
        """
        Extract the video ID from a TikTok URL.
        Example: https://www.tiktok.com/@username/video/1234567890123456789
        """
        m = re.search(r'/video/(\d+)', url)
        if not m:
            raise ValueError(f"Could not extract video ID from URL: {url}")
        return m.group(1)

    def construct_post_url(self, username: str, video_id: str) -> str:
        """
        Construct a TikTok post URL from a username and video ID.
        """
        return f"https://www.tiktok.com/@{username}/video/{video_id}"

    def get_post_details(self, post_url: str) -> dict:
        """
        Fetches details for a single TikTok video.

        Args:
            post_url: URL of the TikTok post

        Returns:
            Dictionary with post details
        """
        # This method is required by the BaseSocialMediaCollector interface
        # but we're focusing only on getting post URLs
        video_id = self.extract_video_id_from_url(post_url)
        return {"id": video_id, "url": post_url}


# === Example Usage ===
if __name__ == "__main__":
    collector = TikTokCollector()

    # Get post URLs for a TikTok user (can use username or URL)
    url = "https://www.tiktok.com/@khaby.lame"
    print(f"Getting post URLs for {url}...")
    posts = collector.get_profile_posts(url, count=5)

    if posts:
        print(f"\nPosts for {url}:")
        for i, post in enumerate(posts, 1):
            print(f"{i}. {post}")