import os
import sys
from typing import List
from dotenv import load_dotenv
from rich import print

# Add the parent directory to sys.path to allow absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_collection.base_collector import BaseSocialMediaCollector

load_dotenv()

class FacebookCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.api_key =  os.getenv('INSTAGRAM_RAPID_API_KEY')
        self.api_host = "facebook-scraper3.p.rapidapi.com"
        self.headers = {
            "x-rapidapi-key": self.api_key,
            "x-rapidapi-host": self.api_host
        }

        # Mock data since API is not available
        self.mock_profile_posts = [
            f"https://www.facebook.com/100064860875397/posts/1163923429113009",
            f"https://www.facebook.com/100064860875397/posts/1163325129172839",
            f"https://www.facebook.com/100064860875397/posts/1163072315864787"
        ]

        self.mock_group_posts = [
            f"https://www.facebook.com/groups/144798092849300/posts/1681658279163266",
            f"https://www.facebook.com/groups/144798092849300/posts/1681650102497417",
            f"https://www.facebook.com/groups/144798092849300/posts/1681650272497400"
        ]

    def extract_id_from_url(self, url: str, is_group: bool = False) -> str:
        """Extract page ID or group ID from a Facebook URL"""
        if "facebook.com" not in url:
            return url

        # Handle URLs with or without protocol
        if "://" in url:
            clean_url = url.split("://")[1]
        else:
            clean_url = url

        # Split by slashes and remove empty parts
        parts = [p for p in clean_url.split('/') if p]

        if is_group and 'groups' in parts:
            # Find the part after "groups"
            groups_idx = parts.index('groups')
            if groups_idx + 1 < len(parts):
                group_id = parts[groups_idx + 1]
                # Remove query parameters
                if '?' in group_id:
                    group_id = group_id.split('?')[0]
                return group_id
        elif not is_group and ('facebook.com' in parts or 'www.facebook.com' in parts):
            # Find the index of facebook.com part
            fb_idx = -1
            if 'facebook.com' in parts:
                fb_idx = parts.index('facebook.com')
            elif 'www.facebook.com' in parts:
                fb_idx = parts.index('www.facebook.com')

            # Get the part after facebook.com if it exists
            if fb_idx >= 0 and fb_idx + 1 < len(parts):
                page_id = parts[fb_idx + 1]
                # Remove query parameters
                if '?' in page_id:
                    page_id = page_id.split('?')[0]
                return page_id

        return url

    def get_post_details(self, post_url: str) -> dict:
        """Get details for a Facebook post"""
        result = {"post_url": post_url}

        try:
            # Extract post ID from the URL
            parts = post_url.strip('/').split('/')
            if 'posts' in parts:
                post_idx = parts.index('posts')
                if post_idx + 1 < len(parts):
                    result["post_id"] = parts[post_idx + 1]

            # Extract page name
            if len(parts) > 2 and 'facebook.com' in parts:
                fb_idx = parts.index('facebook.com')
                if fb_idx + 1 < len(parts) and parts[fb_idx + 1] not in ['posts', 'permalink.php']:
                    result["page_name"] = parts[fb_idx + 1]
        except Exception:
            pass

        return result

    def get_company_posts(self, url: str) -> List[str]:
        """Get all post URLs from a Facebook company page"""
        page_id = self.extract_id_from_url(url)
        print(f"Fetching posts for Facebook page: {page_id}")
        return self.mock_profile_posts

    def get_profile_posts(self, url: str) -> List[str]:
        """Get all post URLs from a Facebook user profile"""
        return self.get_company_posts(url)

    def get_group_posts(self, url: str) -> List[str]:
        """Get all post URLs from a Facebook group"""
        group_id = self.extract_id_from_url(url, is_group=True)
        print(f"Fetching posts for Facebook group: {group_id}")
        return self.mock_group_posts

# Example usage
if __name__ == "__main__":
    collector = FacebookCollector()

    # Example profile URL
    profile_url = "https://www.facebook.com/100064860875397"

    # Get post URLs for the user
    print(f"User posts for {profile_url}:")
    profile_posts = collector.get_profile_posts(profile_url)
    for post_url in profile_posts:
        print(post_url)

    print("\n" + "-"*50 + "\n")

    # Example group URL
    group_url = "https://www.facebook.com/groups/144798092849300"

    # Get post URLs for the group
    print(f"Group posts for {group_url}:")
    group_posts = collector.get_group_posts(group_url)
    if group_posts:
        for post_url in group_posts:
            print(post_url)
    else:
        print("No group posts found or API endpoint not available.")