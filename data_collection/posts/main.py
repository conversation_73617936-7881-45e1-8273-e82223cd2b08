from data_collection.posts.instagram import Instagram<PERSON>ollector
from data_collection.tiktok import <PERSON><PERSON><PERSON>ok<PERSON>ollector
from data_collection.posts.facebook import FacebookCollector
from data_collection.posts.linkedin import LinkedInCollector
from dotenv import load_dotenv
from typing import Dict, List

# Load environment variables from .env file
load_dotenv()

class SocialMediaDataCollector:
    """
    A unified interface for collecting data from multiple social media platforms.

    This class provides a centralized way to collect post URLs and related data
    from various social media platforms including Instagram, Facebook, TikTok,
    and LinkedIn using their respective APIs through RapidAPI.

    Attributes:
        instagram_collector: InstagramCollector instance for Instagram API access
        facebook_collector: FacebookCollector instance for Facebook API access
        linkedin_collector: LinkedInCollector instance for LinkedIn API access
        tiktok_collector: TikTokCollector instance for TikTok API access

    Environment Variables Required:
        INSTAGRAM_RAPID_API_KEY: API key for Instagram
        FACEBOOK_RAPID_API_KEY: API key for Facebook
        LINKEDIN_RAPID_API_KEY: API key for LinkedIn
        TIKTOK_RAPID_API_KEY: API key for TikTok
    """

    def __init__(self):
        """
        Initialize the social media data collector with API keys from environment variables.

        Loads API keys for all supported platforms from environment variables and
        initializes collector instances for each platform.
        Each API key should be set in the .env file before instantiating this class.
        """
        # Initialize collectors for each platform
        self.instagram_collector = InstagramCollector()
        self.facebook_collector = FacebookCollector()
        self.linkedin_collector = LinkedInCollector()
        self.tiktok_collector = TikTokCollector()

    def get_instagram_post_urls(self, url: str, count: int = 10) -> List[str]:
        """
        Collect post URLs from an Instagram company profile.

        Args:
            url (str): Instagram URL or username of the company
            count (int): Maximum number of posts to retrieve

        Returns:
            List[str]: List of Instagram post URLs
        """
        return self.instagram_collector.get_company_posts(url, count)

    def get_facebook_post_urls(self, url: str) -> List[str]:
        """
        Collect post URLs from a Facebook company page.

        Args:
            url (str): Facebook URL or page name of the company

        Returns:
            List[str]: List of Facebook post URLs
        """
        return self.facebook_collector.get_company_posts(url)

    def get_facebook_group_post_urls(self, url: str) -> List[str]:
        """
        Collect post URLs from a Facebook group.

        Args:
            url (str): Facebook group URL or group ID

        Returns:
            List[str]: List of Facebook group post URLs
        """
        return self.facebook_collector.get_group_posts(url)

    def get_tiktok_post_urls(self, url: str, count: int = 10) -> List[str]:
        """
        Collect post URLs from a TikTok company profile.

        Args:
            url (str): TikTok URL or username of the company
            count (int): Maximum number of posts to retrieve

        Returns:
            List[str]: List of TikTok post URLs
        """
        return self.tiktok_collector.get_company_posts(url, count)

    def get_linkedin_post_urls(self, url: str) -> List[str]:
        """
        Collect post URLs from a LinkedIn company page.

        Args:
            url (str): LinkedIn URL or company name

        Returns:
            List[str]: List of LinkedIn post URLs
        """
        return self.linkedin_collector.get_company_posts(url)

    def get_all_platform_post_urls(self, url: str, count_per_platform: int = 10, include_facebook_groups: bool = False) -> Dict[str, List[str]]:
        """
        Collect post URLs from all social media platforms for a given company.

        Args:
            url (str): Company URL or name to search for on all platforms
            count_per_platform (int): Maximum number of posts to retrieve per platform
            include_facebook_groups (bool): Whether to include Facebook group posts (if URL is a Facebook group)

        Returns:
            Dict[str, List[str]]: Dictionary with platform names as keys and lists of post URLs as values
        """
        result = {}

        # Get Instagram post URLs
        print(f"Collecting Instagram posts for {url}...")
        instagram_posts = self.get_instagram_post_urls(url, count_per_platform)
        result["instagram"] = instagram_posts

        # Get Facebook post URLs
        print(f"Collecting Facebook posts for {url}...")
        facebook_posts = self.get_facebook_post_urls(url)
        result["facebook"] = facebook_posts

        # Get Facebook group posts if requested and URL contains "facebook.com/groups"
        if include_facebook_groups and "facebook.com/groups" in url:
            print(f"Collecting Facebook group posts for {url}...")
            facebook_group_posts = self.get_facebook_group_post_urls(url)
            result["facebook_groups"] = facebook_group_posts

        # Get TikTok post URLs
        print(f"Collecting TikTok posts for {url}...")
        tiktok_posts = self.get_tiktok_post_urls(url, count_per_platform)
        result["tiktok"] = tiktok_posts

        # Get LinkedIn post URLs
        print(f"Collecting LinkedIn posts for {url}...")
        linkedin_posts = self.get_linkedin_post_urls(url)
        result["linkedin"] = linkedin_posts

        return result


if __name__ == '__main__':
    # Example usage of the SocialMediaDataCollector
    collector = SocialMediaDataCollector()

    # Company URL or name to search for on all platforms
    company_url = "https://www.instagram.com/amazon"

    
    # Get post URLs from all platforms
    print(f"Collecting post URLs for '{company_url}' from all social media platforms...\n")
    all_posts = collector.get_all_platform_post_urls(company_url)
    
    
    # Display the results
    for platform, posts in all_posts.items():
        print(f"\n{platform.upper()} POSTS ({len(posts)} found):")
        if posts:
            for i, post_url in enumerate(posts, 1):
                print(f"  {i}. {post_url}")
        else:
            print(f"  No posts found for {company_url} on {platform}")

    # Example of getting posts from a specific platform
    print("\n\nExample of getting posts from a specific platform:")

    # Get LinkedIn posts for a different company
    linkedin_url = "microsoft"  # Can also be a URL like "https://www.linkedin.com/company/microsoft"
    print(f"\nCollecting LinkedIn posts for {linkedin_url}...")
    linkedin_posts = collector.get_linkedin_post_urls(linkedin_url)
    
    # Display the results
    print(f"\nLINKEDIN POSTS for {linkedin_url} ({len(linkedin_posts)} found):")
    if linkedin_posts:
        for i, post_url in enumerate(linkedin_posts, 1):
            print(f"  {i}. {post_url}")
    else:
        print(f"  No posts found for {linkedin_url} on LinkedIn")

    # Get Facebook group posts - using a public group
    facebook_group_url = "https://www.facebook.com/groups/144798092849300"  # Poisons Help Emergency ID group
    print(f"\nCollecting Facebook group posts for {facebook_group_url}...")
    facebook_group_posts = collector.get_facebook_group_post_urls(facebook_group_url)

    # Display the results
    print(f"\nFACEBOOK GROUP POSTS for {facebook_group_url} ({len(facebook_group_posts)} found):")
    if facebook_group_posts:
        for i, post_url in enumerate(facebook_group_posts, 1):
            print(f"  {i}. {post_url}")
    else:
        print(f"  No posts found for {facebook_group_url} or API endpoint not available.")

    # Example of getting posts from all platforms including Facebook groups
    print("\n\nExample of getting posts from all platforms including Facebook groups:")

    # Get all platform posts for a Facebook group
    print(f"\nCollecting posts from all platforms for {facebook_group_url}...")
    all_platform_posts = collector.get_all_platform_post_urls(facebook_group_url, include_facebook_groups=True)
    

    # Display the results
    for platform, posts in all_platform_posts.items():
        print(f"\n{platform.upper()} POSTS ({len(posts)} found):")
        if posts:
            for i, post_url in enumerate(posts, 1):
                print(f"  {i}. {post_url}")
        else:
            print(f"  No posts found for {facebook_group_url} on {platform}")