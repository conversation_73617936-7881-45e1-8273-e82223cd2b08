# Fixed code for Google Colab - arranged by Solved4You 2.0
from telethon import TelegramClient
from telethon.tl.functions.messages import GetDialogsRequest
from telethon.tl.types import InputPeerEmpty, InputPeerChannel, InputPeerUser
from telethon.errors.rpcerrorlist import <PERSON>eer<PERSON><PERSON>od<PERSON>rror, UserPrivacyRestrictedError, SessionPasswordNeededError
from telethon.tl.functions.channels import InviteToChannelRequest
from typing import List, Dict, Optional, Any
import logging
import asyncio
from dataclasses import dataclass
from logger_config import setup_logger

logger = setup_logger()

@dataclass
class TelegramConfig:
    """Configuration for Telegram client"""
    api_id: str
    api_hash: str
    phone: str
    session_name: str = 'telegram_scrape'

@dataclass
class TelegramMember:
    """Data structure for Telegram member information"""
    username: str
    user_id: int
    first_name: str
    last_name: str
    group: str
    group_id: str

    @property
    def full_name(self) -> str:
        """Get the full name of the member"""
        return f"{self.first_name} {self.last_name}".strip()

    def to_dict(self) -> Dict[str, Any]:
        """Convert member data to dictionary"""
        return {
            'username': self.username,
            'user_id': self.user_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'group': self.group,
            'group_id': self.group_id
        }

class TelegramScraper:
    """Telegram scraper for collecting member information from groups/channels"""
    
    def __init__(self, config: TelegramConfig):
        self.config = config
        self.client = TelegramClient(config.session_name, config.api_id, config.api_hash)

    async def connect(self) -> None:
        """Connect to Telegram and handle authentication"""
        try:
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                await self.client.send_code_request(self.config.phone)
                code = input('Enter the SMS code: ')
                
                try:
                    await self.client.sign_in(self.config.phone, code)
                except SessionPasswordNeededError:
                    password = input('Enter your 2FA password: ')
                    await self.client.sign_in(password=password)
                    
            logger.info("Successfully connected to Telegram")
            
        except Exception as e:
            logger.error(f"Failed to connect to Telegram: {str(e)}")
            raise

    async def scrape_members(self, target: str) -> List[Dict[str, Any]]:
        """
        Scrape members from a target group/channel
        
        Args:
            target: The target group/channel username or ID
            
        Returns:
            List of dictionaries containing member information
        """
        try:
            logger.info(f"Fetching members from {target}")
            
            # Get participants
            all_participants = await self.client.get_participants(target, aggressive=True)
            
            # Convert to structured data
            members = []
            for user in all_participants:
                member = TelegramMember(
                    username=user.username or "",
                    user_id=user.id,
                    first_name=user.first_name or "",
                    last_name=user.last_name or "",
                    group=target,
                    group_id='groupid'  # You might want to get the actual group ID
                )
                members.append(member.to_dict())
            
            logger.info(f"Successfully scraped {len(members)} members from {target}")
            return members
            
        except Exception as e:
            logger.error(f"Error scraping members: {str(e)}")
            raise

    async def close(self) -> None:
        """Close the Telegram client connection"""
        await self.client.disconnect()
        logger.info("Telegram client disconnected")

async def main():
    """Main function to run the scraper"""
    config = TelegramConfig(
        api_id='27928447',
        api_hash='f9ab963bd50e9669e6ea7d7da4da1e0f',
        phone='+2349017491405'
    )
    
    scraper = TelegramScraper(config)
    
    try:
        await scraper.connect()
        members = await scraper.scrape_members('@CryptoFlake')
        return members
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())