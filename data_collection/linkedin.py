import re
import os
import sys
import requests
from dotenv import load_dotenv
from collections import defaultdict
from rich import print

# Add the parent directory to sys.path to allow absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_collection.base_collector import BaseSocialMediaCollector



load_dotenv()


class LinkedInCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.rapidapi_key = os.getenv('LINKEDIN_RAPID_API_KEY') 
        self.headers = {
            "x-rapidapi-key": self.rapidapi_key,
            "x-rapidapi-host": "linkedin-data-api.p.rapidapi.com"
        }

    def get_post_details(self, post_url: str) -> defaultdict:
        """
        Get comments for a LinkedIn post and organize them by user.

        Args:
            post_url: URL of the LinkedIn post

        Returns:
            A defaultdict with usernames as keys and lists of comments as values
        """
        # Extract post ID from URL
        match = re.search(r'activity[-:](\d+)', post_url)
        post_id = match.group(1) if match else None

        if not post_id:
            print(f"Could not extract post ID from URL: {post_url}")
            return defaultdict(list)

        # Return an empty defaultdict
        return defaultdict(list)



    def get_company_posts(self, url: str):
        """
        Get all post URLs from a LinkedIn company page.

        Args:
            url: LinkedIn company URL or name

        Returns:
            A list of post URLs
        """
        try:
            # Extract company name from URL if it's a URL
            company_name = url

            # Check if it's a LinkedIn URL
            if "linkedin.com" in url:
                # Extract company name from URL like https://www.linkedin.com/company/company-name
                if "company/" in url:
                    parts = url.split("company/")
                    if len(parts) > 1:
                        company_name = parts[1]
                        # Remove any trailing parameters or slashes
                        if '?' in company_name:
                            company_name = company_name.split('?')[0]
                        if '/' in company_name:
                            company_name = company_name.split('/')[0]

                        print(f"Extracted company name from URL: {company_name}")

            api_url = "https://linkedin-data-api.p.rapidapi.com/get-company-posts"

            querystring = {"username": company_name, "start": "0"}

            print(f"Fetching posts for LinkedIn company: {url}")
            response = requests.get(api_url, headers=self.headers, params=querystring, timeout=15)

            # Check if the request was successful
            if response.status_code == 200:
                company_posts = []

                # Parse the JSON response
                response_data = response.json()

                # Get the data array from the response, or an empty list if it doesn't exist
                datas = response_data.get('data', [])

                # Extract post URLs from the data
                for data in datas:
                    if isinstance(data, dict) and 'postUrl' in data:
                        company_posts.append(data['postUrl'])

                return company_posts
            else:
                print(f"API request failed with status code: {response.status_code}")
                if hasattr(response, 'text'):
                    print(f"Response: {response.text}")
                return []

        except Exception as e:
            print(f"Error getting company posts: {e}")
            return []


    def get_profile_posts(self, url: str):
        """
        Get all post URLs from a LinkedIn user profile.

        Args:
            url: LinkedIn URL or username

        Returns:
            A list of post URLs
        """
        try:
            # Extract username from URL if it's a URL
            username = url

            # Check if it's a LinkedIn URL
            if "linkedin.com" in url:
                # Extract username from URL like https://www.linkedin.com/in/username
                if "/in/" in url:
                    parts = url.split("/in/")
                    if len(parts) > 1:
                        username = parts[1]
                        # Remove any trailing parameters or slashes
                        if '?' in username:
                            username = username.split('?')[0]
                        if '/' in username:
                            username = username.split('/')[0]

                        print(f"Extracted username from URL: {username}")

            api_url = "https://linkedin-data-api.p.rapidapi.com/get-profile-posts"

            querystring = {"username": username}

            print(f"Fetching posts for LinkedIn user: {url}")
            response = requests.get(api_url, headers=self.headers, params=querystring, timeout=15)

            # Check if the request was successful
            if response.status_code == 200:
                profile_posts = []

                # Parse the JSON response
                response_data = response.json()

                # Get the data array from the response, or an empty list if it doesn't exist
                datas = response_data.get('data', [])

                # Extract post URLs from the data
                for data in datas:
                    if isinstance(data, dict) and 'postUrl' in data:
                        profile_posts.append(data['postUrl'])

                return profile_posts
            else:
                print(f"API request failed with status code: {response.status_code}")
                if hasattr(response, 'text'):
                    print(f"Response: {response.text}")
                return []

        except Exception as e:
            print(f"Error getting profile posts: {e}")
            return []



# Example usage
if __name__ == "__main__":
    # Initialize the collector
    collector = LinkedInCollector()

    # Example profile name or URL
    profile_url = "https://www.linkedin.com/in/adamselipsky"  # Can also be just the username: "adamselipsky"

    print(f"Getting all post URLs for LinkedIn user: {profile_url}")

    # Get post URLs for the user
    profile_posts = collector.get_profile_posts(url=profile_url)

    # Display the results
    if profile_posts:
        print(f"\nFound {len(profile_posts)} posts for {profile_url}")
        print("\nPost URLs:")
        for i, post_url in enumerate(profile_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {profile_url} or an error occurred.")

    # Example company URL or name
    company_url = "https://www.linkedin.com/company/amazon"  # Can also be just the company name: "amazon"
    print(f"\nGetting all post URLs for LinkedIn company: {company_url}")

    # Get post URLs for the company
    company_posts = collector.get_company_posts(company_url)

    # Display the results
    if company_posts:
        print(f"\nFound {len(company_posts)} posts for {company_url}")
        print("\nPost URLs:")
        for i, post_url in enumerate(company_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {company_url} or an error occurred.")