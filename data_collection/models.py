from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, func
from .database import Base
import uuid

def generate_job_id():
    return f"job_{uuid.uuid4().hex[:8]}"

class ParentJob(Base):
    __tablename__ = 'parent_jobs'

    id = Column(Integer, primary_key=True)
    job_id = Column(String(50), unique=True, default=generate_job_id)
    business_domain = Column(String(255))
    business_goals = Column(Text)
    target_market = Column(Text)
    business_website_url = Column(String(500))
    additional_criteria = Column(Text)
    status = Column(String(50), default='scheduled')
    estimated_completion_time = Column(String(50))
    actual_completion_time = Column(DateTime)
    total_leads_requested = Column(Integer)
    total_leads_scraped = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class ChildJob(Base):
    __tablename__ = 'child_jobs'

    id = Column(Integer, primary_key=True)
    job_id = Column(String(50), unique=True, default=generate_job_id)
    parent_job_id = Column(String(50), ForeignKey('parent_jobs.job_id'))
    platform = Column(String(20))
    status = Column(String(50), default='scheduled')
    leads_requested = Column(Integer)
    leads_scraped = Column(Integer, default=0)
    scraping_criteria = Column(JSON)
    error_message = Column(Text)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class ScrapedUser(Base):
    __tablename__ = 'scraped_users'

    id = Column(Integer, primary_key=True)
    child_job_id = Column(String(50), ForeignKey('child_jobs.job_id'))
    platform = Column(String(20))
    user_id = Column(String(100))
    username = Column(String(100))
    display_name = Column(String(255))
    bio = Column(Text)
    follower_count = Column(Integer)
    following_count = Column(Integer)
    profile_url = Column(String(500))
    profile_image_url = Column(String(500))
    location = Column(String(255))
    verified = Column(Boolean, default=False)
    additional_data = Column(JSON)
    scraped_at = Column(DateTime, default=func.now())

class GroupLink(Base):
    __tablename__ = 'group_links'

    id = Column(Integer, primary_key=True)
    child_job_id = Column(String(50), ForeignKey('child_jobs.job_id'))
    platform = Column(String(20))
    group_name = Column(String(255))
    group_link = Column(String(500))
    group_id = Column(String(100))
    member_count = Column(Integer)
    join_status = Column(String(50), default='pending')
    manual_join_required = Column(Boolean, default=True)
    scraped_at = Column(DateTime, default=func.now())

class JobLog(Base):
    __tablename__ = 'job_logs'

    id = Column(Integer, primary_key=True)
    job_id = Column(String(50))
    job_type = Column(String(20))
    log_level = Column(String(20))
    message = Column(Text)
    additional_data = Column(JSON)
    created_at = Column(DateTime, default=func.now())

class AdminAction(Base):
    __tablename__ = 'admin_actions'

    id = Column(Integer, primary_key=True)
    job_id = Column(String(50))
    action_type = Column(String(50))
    description = Column(Text)
    performed_by = Column(String(100))
    performed_at = Column(DateTime, default=func.now()) 