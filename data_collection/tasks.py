import dramatiq
from dramatiq.brokers.redis import Redis<PERSON>roker
from sqlalchemy.orm import Session
from datetime import datetime
import json
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, GroupLink, ScrapedUser
from .database import get_db_session, test_db_connection
from .web_search.social_links_extractor import SocialMediaPipeline
from .web_search.platform_hanlders.telegram import TelegramHandler
from .web_search.platform_hanlders.whatsapp import WhatsAppHandler
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Configure Dramatiq broker with Redis
redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = int(os.getenv('REDIS_PORT', '6379'))
redis_db = int(os.getenv('REDIS_DB', '0'))

redis_broker = RedisBroker(
    host=redis_host,
    port=redis_port,
    db=redis_db
)
dramatiq.set_broker(redis_broker)

# Test database connection on module load
if not test_db_connection():
    logger.error("Failed to connect to database. Please check your database configuration.")
    raise Exception("Database connection failed")

@dramatiq.actor
def process_parent_job(parent_job_id: str):
    """Process a parent job by creating and initiating child jobs."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Update parent job status
        parent_job.status = 'pending'
        db.commit()

        # Create child jobs for each platform
        platforms = ['facebook', 'instagram', 'linkedin', 'tiktok', 'telegram', 'whatsapp']
        for platform in platforms:
            child_job = ChildJob(
                parent_job_id=parent_job_id,
                platform=platform,
                leads_requested=parent_job.total_leads_requested // len(platforms),
                scraping_criteria=json.dumps({
                    'business_domain': parent_job.business_domain,
                    'business_goals': parent_job.business_goals,
                    'target_market': parent_job.target_market,
                    'additional_criteria': parent_job.additional_criteria
                })
            )
            db.add(child_job)
            db.commit()

            # Start child job processing
            process_child_job.send(child_job.job_id)

        # Log the job creation
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='info',
            message='Parent job processing started'
        )
        db.add(log)
        db.commit()

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error processing parent job: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

@dramatiq.actor
def process_child_job(child_job_id: str):
    """Process a child job for a specific platform."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        # Update child job status
        child_job.status = 'in_progress'
        child_job.started_at = datetime.now()
        db.commit()

        # Initialize appropriate handler based on platform
        if child_job.platform in ['telegram', 'whatsapp']:
            process_group_based_platform.send(child_job_id)
        else:
            process_direct_platform.send(child_job_id)

    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing child job: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()


@dramatiq.actor
def process_group_based_platform(child_job_id: str):
    """Process platforms that require group joining (Telegram/WhatsApp)."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        # Initialize appropriate handler
        if child_job.platform == 'telegram':
            handler = TelegramHandler()
        else:
            handler = WhatsAppHandler()

        # Search for groups
        groups = handler.search_groups(child_job.scraping_criteria)
        
        # Store group links
        for group in groups:
            group_link = GroupLink(
                child_job_id=child_job_id,
                platform=child_job.platform,
                group_name=group.get('title', ''),
                group_link=group.get('url', ''),
                member_count=group.get('member_count', 0)
            )
            db.add(group_link)
        
        # Update job status
        child_job.status = 'awaiting_manual_action'
        db.commit()

        # Send email notification
        send_manual_action_email(child_job_id)

    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing group-based platform: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

@dramatiq.actor
def process_direct_platform(child_job_id: str):
    """Process platforms with direct API access (Facebook, Instagram, LinkedIn, TikTok)."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        # Initialize social media pipeline
        pipeline = SocialMediaPipeline()
        
        # Extract company info
        company_info = pipeline.extract_company_info_with_llm(
            website_content=None,
            company_goals=child_job.scraping_criteria
        )

        # Search for users based on platform
        if child_job.platform == 'facebook':
            users = pipeline.search_facebook_users(company_info)
        elif child_job.platform == 'instagram':
            users = pipeline.search_instagram_users(company_info)
        elif child_job.platform == 'linkedin':
            users = pipeline.search_linkedin_users(company_info)
        elif child_job.platform == 'tiktok':
            users = pipeline.search_tiktok_users(company_info)

        # Store scraped users
        for user in users:
            scraped_user = ScrapedUser(
                child_job_id=child_job_id,
                platform=child_job.platform,
                user_id=user.get('id'),
                username=user.get('username'),
                display_name=user.get('name'),
                bio=user.get('bio'),
                follower_count=user.get('followers'),
                following_count=user.get('following'),
                profile_url=user.get('profile_url'),
                profile_image_url=user.get('profile_image'),
                location=user.get('location'),
                verified=user.get('verified', False),
                additional_data=user
            )
            db.add(scraped_user)

        # Update job status
        child_job.status = 'completed'
        child_job.completed_at = datetime.now()
        child_job.leads_scraped = len(users)
        db.commit()

        # Check if all child jobs are completed
        check_parent_job_completion.send(child_job.parent_job_id)

    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error processing direct platform: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

@dramatiq.actor
def check_parent_job_completion(parent_job_id: str):
    """Check if all child jobs are completed and update parent job status."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Check all child jobs
        child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == parent_job_id).all()
        all_completed = all(job.status == 'completed' for job in child_jobs)
        
        if all_completed:
            parent_job.status = 'completed'
            parent_job.actual_completion_time = datetime.now()
            parent_job.total_leads_scraped = sum(job.leads_scraped for job in child_jobs)
            db.commit()

            # Send completion notification
            send_completion_email(parent_job_id)

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error checking parent job completion: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()

def send_manual_action_email(child_job_id: str):
    """Send email notification for manual group joining."""
    db = get_db_session()
    try:
        child_job = db.query(ChildJob).filter(ChildJob.job_id == child_job_id).first()
        if not child_job:
            return

        parent_job = db.query(ParentJob).filter(ParentJob.job_id == child_job.parent_job_id).first()
        groups = db.query(GroupLink).filter(GroupLink.child_job_id == child_job_id).all()

        # Create email content
        msg = MIMEMultipart()
        msg['Subject'] = f'Manual Action Required - {child_job.platform.title()} Group Joining'
        msg['From'] = os.getenv('SMTP_FROM')
        msg['To'] = os.getenv('ADMIN_EMAIL')

        body = f"""
        Dear Admin,

        Parent Job ID: {parent_job.job_id}
        Business Domain: {parent_job.business_domain}

        The following groups require manual joining:

        {child_job.platform.upper()} GROUPS:
        {chr(10).join([f'- Group Name: {group.group_name}\n  Group Link: {group.group_link}\n  Job ID: {child_job_id}' for group in groups])}

        Please join these groups and then update the job status to "manual_action_completed" in the admin panel.

        Current Status: Awaiting Manual Action
        Next Steps: Join groups → Update status → Automatic member scraping will resume
        """

        msg.attach(MIMEText(body, 'plain'))

        # Send email
        with smtplib.SMTP(os.getenv('SMTP_HOST'), int(os.getenv('SMTP_PORT'))) as server:
            server.starttls()
            server.login(os.getenv('SMTP_USER'), os.getenv('SMTP_PASSWORD'))
            server.send_message(msg)

    except Exception as e:
        log = JobLog(
            job_id=child_job_id,
            job_type='child',
            log_level='error',
            message=f'Error sending manual action email: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close()


def send_completion_email(parent_job_id: str):
    """Send email notification for job completion."""
    db = get_db_session()
    try:
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == parent_job_id).first()
        if not parent_job:
            return

        # Create email content
        msg = MIMEMultipart()
        msg['Subject'] = 'Scraping Job Completed'
        msg['From'] = os.getenv('SMTP_FROM')
        msg['To'] = os.getenv('ADMIN_EMAIL')

        body = f"""
        Dear Admin,

        The scraping job has been completed successfully.

        Job Details:
        - Job ID: {parent_job.job_id}
        - Business Domain: {parent_job.business_domain}
        - Total Leads Requested: {parent_job.total_leads_requested}
        - Total Leads Scraped: {parent_job.total_leads_scraped}
        - Completion Time: {parent_job.actual_completion_time}

        You can view the detailed results in the admin panel.
        """

        msg.attach(MIMEText(body, 'plain'))

        # Send email
        with smtplib.SMTP(os.getenv('SMTP_HOST'), int(os.getenv('SMTP_PORT'))) as server:
            server.starttls()
            server.login(os.getenv('SMTP_USER'), os.getenv('SMTP_PASSWORD'))
            server.send_message(msg)

    except Exception as e:
        log = JobLog(
            job_id=parent_job_id,
            job_type='parent',
            log_level='error',
            message=f'Error sending completion email: {str(e)}'
        )
        db.add(log)
        db.commit()
    finally:
        db.close() 