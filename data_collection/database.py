from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.declarative import declarative_base

from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Database configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://postgres:1234@localhost:5432/social_scraping')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5432')
DB_USER = os.getenv('DB_USER', 'postgres')
DB_PASSWORD = os.getenv('DB_PASSWORD', '1234')
DB_NAME = os.getenv('DB_NAME', 'social_scraping')

# Create engine with connection pooling and proper configuration
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=300,    # Recycle connections every 5 minutes
    pool_size=10,        # Number of connections to maintain
    max_overflow=20,     # Additional connections that can be created
    echo=False           # Set to True for SQL debugging
)


# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

def get_db() -> Session:
    """
    Dependency function to get database session.
    Use this in FastAPI endpoints and Dramatiq tasks.
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_session() -> Session:
    """
    Get a database session for use in Dramatiq tasks.
    Remember to close the session when done.
    """
    return SessionLocal()

def init_db():
    """Initialize the database by creating all tables."""
    try:
        logger.info("Initializing database...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

def test_db_connection():
    """Test database connection."""
    try:
        with engine.connect() as connection:
           

            connection.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

# Event listeners for connection management
@event.listens_for(engine, "connect")
def set_postgresql_settings(dbapi_connection, connection_record):
    """Set database-specific configurations on connect."""
    if 'postgresql' in str(engine.url):
        # PostgreSQL specific settings can be added here
        logger.debug("PostgreSQL connection established")
        pass