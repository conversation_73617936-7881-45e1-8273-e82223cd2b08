import requests
import re
import os
import sys
from typing import Dict, List
from dotenv import load_dotenv

# Add the parent directory to sys.path to allow absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_collection.base_collector import BaseSocialMediaCollector


load_dotenv()


class InstagramCollector(BaseSocialMediaCollector):
    def __init__(self):
        self.api_key = os.getenv('INSTAGRAM_RAPID_API_KEY')
        print(f"API Key: {self.api_key}")
        # Use different API hosts for different endpoints
        self.post_details_api_host = "instagram-scraper-stable-api.p.rapidapi.com"
        self.user_posts_api_host = "instagram230.p.rapidapi.com"

        # Default headers for post details
        self.post_details_headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.post_details_api_host
        }

        # Headers for user posts
        self.user_posts_headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.user_posts_api_host
        }

    def extract_shortcode_from_url(self, url: str) -> str:
        """Extract shortcode from Instagram URL"""
        shortcode_pattern = r'instagram\.com/p/([A-Za-z0-9_-]+)'
        shortcode_match = re.search(shortcode_pattern, url)

        if shortcode_match:
            return shortcode_match.group(1)
        raise ValueError(f"Could not extract shortcode from URL: {url}")

    def get_post_details(self, post_url: str) -> Dict:
        """Get post details - focusing only on returning the post URL"""
        try:
            # Use the full post URL directly
            if not post_url.startswith(('http://', 'https://')):
                # If it's just a shortcode, convert it to a full URL
                post_url = f"https://www.instagram.com/p/{post_url}/"

            url = f"https://{self.post_details_api_host}/get_media_data.php"
            querystring = {
                "reel_post_code_or_url": post_url,
                "type": "post"
            }

            # Make the API request
            response = requests.get(
                url,
                headers=self.post_details_headers,
                params=querystring,
                timeout=15
            )

            # Extract shortcode from URL if it's a full URL
            shortcode = self.extract_shortcode_from_url(post_url) if post_url.startswith(('http://', 'https://')) else post_url

            # If we get a successful response, process it
            if response.status_code == 200:
                # As per the request, we're focusing only on returning the post URL
                return {
                    "shortcode": shortcode,
                    "post_url": post_url
                }
            # Return the post URL regardless of API response
            return {
                "shortcode": shortcode,
                "post_url": post_url
            }

        except Exception:
            # Return an empty dictionary in case of error
            return {}

    def get_profile_posts(self, url: str, count: int = 20) -> List[str]:
        """
        Get all post URLs for a profile.

        Args:
            url: Instagram URL or username
            count: Number of posts to retrieve (default: 20)

        Returns:
            List of Instagram post URLs
        """
        try:
            # Extract username from URL if it's a URL, or use as is
            username = url

            # If it's an Instagram URL, extract the username
            if "instagram.com" in url:
                # Extract username from URL like https://www.instagram.com/username/
                match = re.search(r'instagram\.com/([^/]+)', url)
                if match:
                    username = match.group(1)
                    # Remove any trailing parameters
                    if '?' in username:
                        username = username.split('?')[0]
                    # Remove trailing slash if present
                    if username.endswith('/'):
                        username = username[:-1]

                    if __name__ == "__main__":
                        print(f"Extracted username from URL: {username}")

            # Remove @ symbol if present in the username
            if username.startswith('@'):
                username = username[1:]

            # Make an API request to get user posts using the provided endpoint
            api_url = f"https://{self.user_posts_api_host}/user/posts"
            querystring = {"username": username}
            if count:
                querystring["limit"] = str(count)

            # Make the API request
            response = requests.get(
                api_url,
                headers=self.user_posts_headers,
                params=querystring,
                timeout=15
            )

            # If we get a successful response, process it
            if response.status_code == 200:
                data = response.json()
                post_urls = []

                # Extract posts from the response
                posts = []
                if isinstance(data, list):
                    posts = data
                elif isinstance(data, dict):
                    if 'data' in data and isinstance(data['data'], list):
                        posts = data['data']
                    elif 'posts' in data and isinstance(data['posts'], list):
                        posts = data['posts']
                    elif 'items' in data and isinstance(data['items'], list):
                        posts = data['items']
                    elif 'result' in data and isinstance(data['result'], list):
                        posts = data['result']

                # Extract post URLs from the posts
                for post in posts:
                    if isinstance(post, dict):
                        # Try to get the post URL directly
                        post_url = None
                        if 'post_url' in post and post['post_url']:
                            post_url = post['post_url']
                        elif 'url' in post and post['url']:
                            post_url = post['url']

                        # If we couldn't get the URL directly, try to construct it from the shortcode
                        if not post_url:
                            shortcode = None
                            for field in ['shortcode', 'code', 'id', 'post_id', 'postId']:
                                if field in post and post[field]:
                                    shortcode = post[field]
                                    break

                            if shortcode:
                                post_url = f"https://www.instagram.com/p/{shortcode}/"

                        if post_url:
                            post_urls.append(post_url)

                # Limit the number of posts if needed
                if len(post_urls) > count:
                    post_urls = post_urls[:count]

                return post_urls

            # If the API request fails, return an empty list
            return []

        except Exception:
            # Return an empty list in case of error
            return []

    def get_company_posts(self, url: str, count: int = 20) -> List[str]:
        """
        Get posts for a company (same as profile posts for Instagram)

        Args:
            url: Instagram company URL or username
            count: Number of posts to retrieve (default: 20)

        Returns:
            List of Instagram post URLs
        """
        return self.get_profile_posts(url, count)


# Example usage
if __name__ == "__main__":
    # Initialize the collector
    collector = InstagramCollector()

    # Example URL or username - using a public account that's likely to have posts
    profile_url = "https://www.instagram.com/cristiano/"
    count = 10  # Number of posts to retrieve

    print(f"Getting {count} post URLs for Instagram user: {profile_url}")

    # Get post URLs for the user
    profile_posts = collector.get_profile_posts(profile_url, count)

    # Display the results
    if profile_posts:
        print(f"\nFound {len(profile_posts)} posts for {profile_url}")
        print("\nPost URLs:")
        for i, post_url in enumerate(profile_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {profile_url} or an error occurred.")

    # Example company URL or name (same as username for Instagram)
    company_url = "instagram"  # Can also be a URL like "https://www.instagram.com/instagram/"
    print(f"\nGetting {count} post URLs for Instagram company: {company_url}")

    # Get post URLs for the company
    company_posts = collector.get_company_posts(company_url, count)

    # Display the results
    if company_posts:
        print(f"\nFound {len(company_posts)} posts for {company_url}")
        print("\nPost URLs:")
        for i, post_url in enumerate(company_posts, 1):
            print(f"{i}. {post_url}")
    else:
        print(f"No posts found for {company_url} or an error occurred.")