from fastapi import FastAP<PERSON>, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from typing import List, Dict
from datetime import datetime
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from .database import get_db, init_db, test_db_connection
from .tasks import process_parent_job
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Social Media Scraping API")

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    logger.info("Starting up Social Media Scraping API...")

    # Test database connection
    if not test_db_connection():
        logger.error("Failed to connect to database")
        raise Exception("Database connection failed")

    # Initialize database tables
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    db_status = test_db_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected"
    }

class ScrapingRequest(BaseModel):
    business_domain: str
    business_goals: str
    target_market: str
    business_website_url: HttpUrl
    target_platforms: List[str]
    leads_per_platform: Dict[str, int]
    additional_criteria: str

class ScrapingResponse(BaseModel):
    success: bool
    parent_job_id: str
    child_job_ids: Dict[str, str]
    estimated_completion_time: str
    status: str
    created_at: datetime

@app.post("/api/scraping/create-job", response_model=ScrapingResponse)
async def create_scraping_job(request: ScrapingRequest, db = Depends(get_db)):
    """Create a new scraping job with parent and child jobs."""
    try:
        # Validate target platforms
        valid_platforms = ['facebook', 'instagram', 'linkedin', 'tiktok', 'telegram', 'whatsapp']
        if not all(platform in valid_platforms for platform in request.target_platforms):
            raise HTTPException(
                status_code=400,
                detail="Invalid platform specified. Valid platforms are: " + ", ".join(valid_platforms)
            )

        # Calculate total leads requested
        total_leads = sum(request.leads_per_platform.values())

        # Create parent job
        parent_job = ParentJob(
            business_domain=request.business_domain,
            business_goals=request.business_goals,
            target_market=request.target_market,
            business_website_url=str(request.business_website_url),
            additional_criteria=request.additional_criteria,
            status='scheduled',
            estimated_completion_time='4-6 hours',
            total_leads_requested=total_leads
        )
        db.add(parent_job)
        db.commit()
        db.refresh(parent_job)

        # Create child jobs
        child_job_ids = {}
        for platform in request.target_platforms:
            child_job = ChildJob(
                parent_job_id=parent_job.job_id,
                platform=platform,
                leads_requested=request.leads_per_platform.get(platform, 0),
                scraping_criteria=json.dumps({
                    'business_domain': request.business_domain,
                    'business_goals': request.business_goals,
                    'target_market': request.target_market,
                    'additional_criteria': request.additional_criteria
                })
            )
            db.add(child_job)
            db.commit()
            db.refresh(child_job)
            child_job_ids[platform] = child_job.job_id

        # Start processing the parent job
        process_parent_job.send(parent_job.job_id)

        return ScrapingResponse(
            success=True,
            parent_job_id=parent_job.job_id,
            child_job_ids=child_job_ids,
            estimated_completion_time='4-6 hours',
            status='scheduled',
            created_at=parent_job.created_at
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/scraping/job/{job_id}")
async def get_job_status(job_id: str, db = Depends(get_db)):
    """Get the status of a scraping job."""
    try:
        # Check if it's a parent job
        parent_job = db.query(ParentJob).filter(ParentJob.job_id == job_id).first()
        if parent_job:
            child_jobs = db.query(ChildJob).filter(ChildJob.parent_job_id == job_id).all()
            return {
                "job_id": parent_job.job_id,
                "type": "parent",
                "status": parent_job.status,
                "total_leads_requested": parent_job.total_leads_requested,
                "total_leads_scraped": parent_job.total_leads_scraped,
                "child_jobs": [
                    {
                        "job_id": child.job_id,
                        "platform": child.platform,
                        "status": child.status,
                        "leads_requested": child.leads_requested,
                        "leads_scraped": child.leads_scraped
                    }
                    for child in child_jobs
                ]
            }

        # Check if it's a child job
        child_job = db.query(ChildJob).filter(ChildJob.job_id == job_id).first()
        if child_job:
            return {
                "job_id": child_job.job_id,
                "type": "child",
                "platform": child_job.platform,
                "status": child_job.status,
                "leads_requested": child_job.leads_requested,
                "leads_scraped": child_job.leads_scraped,
                "parent_job_id": child_job.parent_job_id
            }

        raise HTTPException(status_code=404, detail="Job not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))