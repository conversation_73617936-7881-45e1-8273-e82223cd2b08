from abc import ABC, abstractmethod
from typing import Dict, List


class BaseSocialMediaCollector(ABC):
    """
    Abstract base class to be implemented for different social media platforms.
    Focused on retrieving data via RapidAPI integrations.
    """

    @abstractmethod
    def get_post_details(self, post_id: str) -> Dict:
        """Fetch comments and data for a specific post."""
        pass

    @abstractmethod
    def get_profile_posts(self, url: str )-> List[Dict]:
        """Fetch posts from a personal profile."""
        pass

    @abstractmethod
    def get_company_posts(self, url: str) -> List[Dict]:
        """Fetch posts from a company profile."""
        pass
