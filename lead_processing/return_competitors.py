import openai
from typing import Dict, List, Tuple
import json
import os
from dotenv import load_dotenv
from website import run

# Load environment variables
load_dotenv()

"""
This module provides functionality to analyze business competitors and their social media presence
using OpenAI's GPT-4 model. It scrapes website content and generates detailed competitor analysis.
"""

class CompetitorAnalyzer:
    """
    A class to analyze business competitors and their social media presence.

    This class takes a business website, domain, and goal as input, scrapes the website content,
    and uses OpenAI's GPT-4 to analyze and return competitor information including their
    social media presence across different platforms.

    Attributes:
        api_key (str): OpenAI API key for making API calls
        client (OpenAI): OpenAI client instance for interacting with OpenAI's API
    """

    def __init__(self, api_key: str = None):
        """
        Initialize the CompetitorAnalyzer with OpenAI API key.

        Args:
            api_key (str, optional): OpenAI API key. If not provided, will try to get from environment.

        Raises:
            ValueError: If no API key is provided and OPENAI_API_KEY environment variable is not set
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key must be provided either through constructor or OPENAI_API_KEY environment variable")
        self.client = openai.OpenAI(api_key=self.api_key)

    def analyze_business(self, business_goal: str, scraped_website: str, domain: str) -> Dict:
        """
        Analyze business information and return competitor details with social media links.

        Args:
            business_goal (str): The goal or mission of the business
            scraped_website (str): Content scraped from the business website
            domain (str): The business domain/category

        Returns:
            Dict: A dictionary containing:
                - business_analysis: Dict with primary_domain, specific_focus, and market_position
                - competitors: List of competitor dictionaries with name and social media links
        """
        analysis_prompt = f"""
        Analyze the following business information and provide detailed competitor analysis:

        Business Goal: {business_goal}
        Website Content: {scraped_website}
        Domain: {domain}

        Please provide a detailed analysis of 5 top competitors in this space, including:
        1. Social media links (Facebook, Instagram, LinkedIn, TikTok)

        Format the response as a JSON object with the following structure:
        {{
            "business_analysis": {{
                "primary_domain": "string",
                "specific_focus": "string",
                "market_position": "string"
            }},
            "competitors": [
                {{
                    "name": "string",
                    "social_media": {{
                        "facebook": "string",
                        "instagram": "string",
                        "linkedin": "string",
                        "tiktok": "string"
                    }}
                }}
            ]
        }}
        """

        analysis_response = self.client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=[
                {"role": "system", "content": "You are a business analysis expert specializing in competitor research and market analysis. Provide accurate and detailed competitor information including their social media presence."},
                {"role": "user", "content": analysis_prompt}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        return json.loads(analysis_response.choices[0].message.content)

    def format_analysis_output(self, results: Dict) -> str:
        """
        Format the analysis results into a readable string.

        Args:
            results (Dict): The analysis results from analyze_business method

        Returns:
            str: Formatted string containing:
                - Business analysis section with primary domain, focus, and market position
                - Competitor sections with names and social media links
        """
        output = []

        # Add business analysis
        output.append("\nBusiness Analysis:")
        output.append(f"Primary Domain: {results['business_analysis']['primary_domain']}")
        output.append(f"Specific Focus: {results['business_analysis']['specific_focus']}")
        output.append(f"Market Position: {results['business_analysis']['market_position']}")

        # Add competitor information
        # output.append("\nCompetitor Analysis:")
        for i, competitor in enumerate(results['competitors'], 1):
            output.append(f"\nCompetitor {i}: {competitor['name']}")
            output.append("Social Media Links:")
            output.append(f"- Facebook: {competitor['social_media']['facebook']}")
            output.append(f"- Instagram: {competitor['social_media']['instagram']}")
            output.append(f"- LinkedIn: {competitor['social_media']['linkedin']}")
            output.append(f"- TikTok: {competitor['social_media']['tiktok']}")

        return '\n'.join(output)

    def extract_business_info(self, url: str) -> Dict:
        """
        Extract business domain and goal from a website URL using OpenAI.

        Args:
            url (str): The website URL to analyze

        Returns:
            Dict: Dictionary containing domain and business_goal
        """
        # Scrape website content
        scraped_website = run(url)

        # Create a prompt for OpenAI to extract domain and business goal
        prompt = f"""
        Analyze the following website content and extract:
        1. The business domain/industry (e.g., agriculture, technology, healthcare)
        2. The business goal/mission

        Website URL: {url}
        Website Content: {scraped_website}

        Format the response as a JSON object with the following structure:
        {{
            "domain": "string",
            "business_goal": "string"
        }}
        """

        # Call OpenAI API
        response = self.client.chat.completions.create(
            model="gpt-4-turbo-preview",
            messages=[
                {"role": "system", "content": "You are a business analyst who extracts business information from website content."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        # Parse the response
        result = json.loads(response.choices[0].message.content)
        return result

    def analyze_and_format(self, url: str, domain: str = None, business_goal: str = None) -> List[Dict]:
        """
        Analyze a business and return competitor information as a list of dictionaries.

        Args:
            url (str): The website URL to analyze
            domain (str, optional): The business domain/category. If None, will be extracted from the website.
            business_goal (str, optional): The goal or mission of the business. If None, will be extracted from the website.

        Returns:
            List[Dict]: List of dictionaries containing competitor information in the format:
            [
                {
                    "name": "Competitor Name",
                    "linkedin": "LinkedIn URL",
                    "instagram": "Instagram URL",
                    "facebook": "Facebook URL",
                    "tiktok": "TikTok URL"
                },
                ...
            ]
        """
        # Scrape website content
        scraped_website = run(url)

        # If domain or business_goal is not provided, extract them from the website
        if domain is None or business_goal is None:
            business_info = self.extract_business_info(url)
            domain = business_info.get("domain") if domain is None else domain
            business_goal = business_info.get("business_goal") if business_goal is None else business_goal

        # Get analysis
        results = self.analyze_business(business_goal, scraped_website, domain)

        # Format results into list of dictionaries
        competitors_list = []

        for competitor in results['competitors']:
            competitor_dict = {
                "name": competitor['name'],
                "linkedin": competitor['social_media']['linkedin'],
                "instagram": competitor['social_media']['instagram'],
                "facebook": competitor['social_media']['facebook'],
                "tiktok": competitor['social_media']['tiktok']
            }
            competitors_list.append(competitor_dict)

        return competitors_list

def main():
    """
    Main function to demonstrate the usage of CompetitorAnalyzer.

    This function:
    1. Initializes the CompetitorAnalyzer
    2. Sets up example business parameters
    3. Analyzes the business and prints competitor information
    """
    # Initialize analyzer
    analyzer = CompetitorAnalyzer()

    # Example usage
    business_goal = "to be one of the best agriculture"
    url = 'dalensai.com'
    domain = "agriculture"

    # Get competitor information
    competitors = analyzer.analyze_and_format(url, domain, business_goal)
    # print(competitors)  # Comment out to prevent terminal output


    # # Access individual competitor information
    # for i, competitor in enumerate(competitors, 1):
    #     print(f"\nCompetitor {i}:")
    #     print(f"Name: {competitor['name']}")
    #     print(f"LinkedIn: {competitor['linkedin']}")
    #     print(f"Instagram: {competitor['instagram']}")
    #     print(f"Facebook: {competitor['facebook']}")
    #     print(f"TikTok: {competitor['tiktok']}")



if __name__ == "__main__":
    main()