import os
import re
import json
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from dotenv import load_dotenv
from openai import OpenAI

# Load API key
load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

"""
This module provides functionality to scrape and analyze business websites.
It extracts business information, social media links, and generates structured analysis
using OpenAI's GPT-4 model.
"""

def scrape_website(url: str) -> dict:
    """
    Scrape a business website and analyze its content.

    Args:
        url (str): The website URL to scrape

    Returns:
        dict: Dictionary containing the scraped business information, or an error message

    The function:
    1. Normalizes the URL and extracts domain information
    2. Scrapes the main page and about page if available
    3. Extracts social media links
    4. Analyzes content using OpenAI's GPT-4
    5. Returns structured business information
    """
    # Normalize URL and extract info
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    domain = urlparse(url).netloc.replace('www.', '')
    company_name = domain.split('.')[0].replace('-', ' ').title()

    # Set up headers and make request
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

    try:
        # Scrape main page
        response = requests.get(url, headers=headers, timeout=5)
        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract text content
        for script in soup(["script", "style"]):
            script.extract()
        text_content = soup.get_text(separator=' ', strip=True)

        # Extract social media links
        social_links = {}
        social_platforms = {
            'facebook': ['facebook.com'], 'twitter': ['twitter.com', 'x.com'],
            'instagram': ['instagram.com'], 'linkedin': ['linkedin.com'],
            'youtube': ['youtube.com'], 'tiktok': ['tiktok.com']
        }

        for link in soup.find_all('a', href=True):
            href = link['href'].lower()
            for platform, domains in social_platforms.items():
                if any(domain in href for domain in domains):
                    social_links[platform] = link['href']

        # Try to find and scrape about page
        about_url = None
        about_keywords = ['about', 'about-us', 'our story', 'who-we-are', 'company']

        for link in soup.find_all('a', href=True):
            href = link['href'].lower()
            link_text = link.get_text().lower()

            if any(keyword in link_text or keyword in href for keyword in about_keywords):
                if href.startswith('/'):
                    about_url = f"{url.rstrip('/')}{href}"
                elif not href.startswith(('http://', 'https://')):
                    about_url = f"{url.rstrip('/')}/{href.lstrip('/')}"
                elif domain in href:
                    about_url = href

                if about_url:
                    try:
                        about_response = requests.get(about_url, headers=headers, timeout=5)
                        about_soup = BeautifulSoup(about_response.text, 'html.parser')
                        for script in about_soup(["script", "style"]):
                            script.extract()
                        about_text = about_soup.get_text(separator=' ', strip=True)
                        text_content += "\n\n" + about_text
                    except:
                        pass
                    break

        # Analyze with AI
        print("Analyzing...")

        # Truncate if too long
        if len(text_content) > 15000:
            text_content = text_content[:15000]

        # Prepare social links text
        social_links_text = ""
        if social_links:
            social_links_text = "Social Media Links:\n"
            for platform, url in social_links.items():
                social_links_text += f"- {platform.capitalize()}: {url}\n"

        # Create prompt for OpenAI
        prompt = f"""
        Extract structured business information from this website content for {company_name}.

        Website: {url}

        {social_links_text}

        Website Content:
        {text_content}

        Extract and organize:
        1. Business Goals: mission, vision, objectives
        2. About the Business: overview, history, founding story
        3. Target Market: ideal customers, demographics, needs
        4. Products/Services: main offerings
        5. Unique Value Proposition: differentiators

        Format as JSON with keys: "business_goals", "about_the_business", "target_market", "products_services", "unique_value_proposition".
        For each key, provide an array of strings as bullet points.
        """

        # Call OpenAI API
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system",
                 "content": "You are a business analyst who extracts structured information from website content."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1500
        )

        # Extract and parse JSON
        ai_response = response.choices[0].message.content.strip()

        # Find JSON in response
        json_match = re.search(r'```json\n(.*?)\n```', ai_response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = ai_response

        try:
            business_info = json.loads(json_str)
        except:
            business_info = {
                "business_goals": ["Information not available"],
                "about_the_business": ["Information not available"],
                "target_market": ["Information not available"],
                "products_services": ["Information not available"],
                "unique_value_proposition": ["Information not available"]
            }

        # Create result dictionary
        result = {
            "company_name": company_name,
            "website": {
                "main": url,
                "about": about_url if about_url else "Not available"
            },
            "social_links": social_links,
            "business_goals": business_info.get('business_goals', ["Information not available"]),
            "about_the_business": business_info.get('about_the_business', ["Information not available"]),
            "target_market": business_info.get('target_market', ["Information not available"]),
            "products_services": business_info.get('products_services', ["Information not available"]),
            "unique_value_proposition": business_info.get('unique_value_proposition', ["Information not available"])
        }

        # Comment out all print statements to reduce terminal output
        # print(f"\nBUSINESS INFORMATION FOR: {company_name.upper()}")
        # print("\n🎯 BUSINESS GOALS:")
        # for goal in business_info.get('business_goals', ["Information not available"]):
        #     print(f"  • {goal}")
        # print("\n🔗 WEBSITE:")
        # print(f"  • Main: {url}")
        # if about_url:
        #     print(f"  • About: {about_url}")
        # if social_links:
        #     print("\n📱 SOCIAL MEDIA:")
        #     for platform, url in social_links.items():
        #         print(f"  • {platform.capitalize()}: {url}")
        # print("\n📋 ABOUT THE BUSINESS:")
        # for paragraph in business_info.get('about_the_business', ["Information not available"]):
        #     print(f"  {paragraph}")
        # print("\n👥 TARGET MARKET:")
        # for target in business_info.get('target_market', ["Information not available"]):
        #     print(f"  • {target}")
        # print("\n🛍️ PRODUCTS/SERVICES:")
        # for product in business_info.get('products_services', ["Information not available"]):
        #     print(f"  • {product}")
        # print("\n💎 UNIQUE VALUE PROPOSITION:")
        # for value in business_info.get('unique_value_proposition', ["Information not available"]):
        #     print(f"  • {value}")

        return result

    except requests.exceptions.Timeout:
        error_msg = "Error: The request timed out."
        # print(error_msg)  # Comment out to prevent terminal output
        return {"error": error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "Error: Could not connect to the website."
        # print(error_msg)  # Comment out to prevent terminal output
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        # print(error_msg)  # Comment out to prevent terminal output
        return {"error": error_msg}


def run(url: str) -> dict:
    """
    Main entry point for website scraping and analysis.

    Args:
        url (str): The website URL to analyze

    Returns:
        dict: Dictionary containing the scraped business information or an error message
    """
    if not url:
        error_msg = "Error: No URL provided. Exiting."
        # print(error_msg)  # Comment out to prevent terminal output
        return {"error": error_msg}

    return scrape_website(url)


if __name__ == "__main__":
    run('dalensai.com')