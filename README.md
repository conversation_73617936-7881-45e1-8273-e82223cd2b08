# Social Media Scraping System

A robust social media scraping system that can extract user data from multiple platforms (Telegram, WhatsApp, Facebook, Instagram, TikTok, and LinkedIn) based on business requirements.

## Features

- Parent-child job structure for unified tracking
- Support for 6 social media platforms
- Automatic scraping for direct API platforms
- Manual intervention workflow for group-based platforms
- Email notifications for manual actions
- Comprehensive job status tracking
- Detailed logging and error handling

## Prerequisites

- Python 3.8+
- PostgreSQL
- Redis
- SMTP server for email notifications

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/social_scraping

# Redis
REDIS_HOST=localhost

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# API Keys
OPENAI_API_KEY=your-openai-api-key
TAVILY_API_KEY=your-tavily-api-key
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd social-media-scraping
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Initialize the database:
```bash
python -c "from data_collection.database import init_db; init_db()"
```

## Running the System

1. Start the Redis server:
```bash
redis-server
```

2. Start the Dramatiq workers:
```bash
dramatiq data_collection.tasks
```

3. Start the FastAPI server:
```bash
uvicorn data_collection.api:app --reload
```

The API will be available at `http://localhost:8000`

## API Usage

### Create a Scraping Job

```bash
curl -X POST "http://localhost:8000/api/scraping/create-job" \
     -H "Content-Type: application/json" \
     -d '{
       "business_domain": "Healthcare Technology",
       "business_goals": "Lead generation for B2B SaaS platform",
       "target_market": "Healthcare professionals and clinic owners",
       "business_website_url": "https://example.com",
       "target_platforms": ["facebook", "instagram", "linkedin", "tiktok", "telegram", "whatsapp"],
       "leads_per_platform": {
         "facebook": 500,
         "instagram": 300,
         "linkedin": 1000,
         "tiktok": 200,
         "telegram": 400,
         "whatsapp": 300
       },
       "additional_criteria": "Only professionals aged 25-45, focus on decision makers in healthcare"
     }'
```

### Check Job Status

```bash
curl "http://localhost:8000/api/scraping/job/{job_id}"
```

## Workflow

1. Submit a scraping request through the API
2. System creates a parent job and child jobs for each platform
3. For direct API platforms (Facebook, Instagram, LinkedIn, TikTok):
   - System automatically scrapes user data
   - Stores results in the database
4. For group-based platforms (WhatsApp, Telegram):
   - System finds relevant groups
   - Sends email notification to admin
   - Waits for admin to join groups
   - Resumes scraping after manual action
5. System tracks progress and sends completion notification

## Database Schema

The system uses the following tables:
- `parent_jobs`: Tracks overall scraping campaigns
- `child_jobs`: Individual platform-specific scraping tasks
- `scraped_users`: Stores scraped user data
- `group_links`: Stores group information for Telegram/WhatsApp
- `job_logs`: Tracks job execution logs
- `admin_actions`: Records manual interventions

## Error Handling

- Comprehensive error logging
- Automatic retry mechanisms
- Email notifications for critical errors
- Manual intervention workflow for group-based platforms

## Security

- Input validation and sanitization
- Rate limiting for platform APIs
- Secure credential management
- Database connection pooling