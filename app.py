#!/usr/bin/env python3
"""
Main application entry point for the Social Media Lead Generation Bot.

This file sets up the FastAPI application with proper database initialization,
Dramatiq worker configuration, and all necessary dependencies.
"""

import uvicorn
import logging
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data_collection.api import app
from data_collection.database import init_db, test_db_connection
from data_collection import tasks  # Import to register Dramatiq actors
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if all required environment variables are set."""
    required_vars = [
        'DATABASE_URL',
        'DB_HOST',
        'DB_PORT', 
        'DB_USER',
        'DB_PASSWORD',
        'DB_NAME',
        'REDIS_HOST'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file and ensure all required variables are set.")
        return False
    
    return True

def initialize_application():
    """Initialize the application with database and other dependencies."""
    logger.info("Initializing Social Media Lead Generation Bot...")
    
    # Check environment variables
    if not check_environment():
        logger.error("Environment check failed. Exiting.")
        sys.exit(1)
    
    # Test database connection
    logger.info("Testing database connection...")
    if not test_db_connection():
        logger.error("Database connection failed. Please check your PostgreSQL configuration.")
        logger.error("Make sure PostgreSQL is running and the connection parameters are correct.")
        sys.exit(1)
    
    # Initialize database tables
    logger.info("Initializing database tables...")
    try:
        init_db()
        logger.info("Database tables initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database tables: {e}")
        sys.exit(1)
    
    logger.info("Application initialized successfully")

def run_api_server():
    """Run the FastAPI server."""
    initialize_application()
    
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', '8000'))
    
    logger.info(f"Starting FastAPI server on {host}:{port}")
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=os.getenv('DEBUG', 'False').lower() == 'true',
        log_level="info"
    )

def run_dramatiq_worker():
    """Run the Dramatiq worker for background tasks."""
    initialize_application()
    
    logger.info("Starting Dramatiq worker...")
    
    # Import dramatiq CLI and run worker
    import dramatiq.cli
    import sys
    
    # Set up dramatiq worker arguments
    sys.argv = [
        'dramatiq',
        'data_collection.tasks',
        '--processes', str(os.getenv('DRAMATIQ_PROCESSES', '4')),
        '--threads', str(os.getenv('DRAMATIQ_THREADS', '8')),
        '--watch', str(project_root / 'data_collection')
    ]
    
    dramatiq.cli.main()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Social Media Lead Generation Bot')
    parser.add_argument(
        'mode',
        choices=['api', 'worker', 'both'],
        help='Run mode: api (FastAPI server), worker (Dramatiq worker), or both'
    )
    
    args = parser.parse_args()
    
    if args.mode == 'api':
        run_api_server()
    elif args.mode == 'worker':
        run_dramatiq_worker()
    elif args.mode == 'both':
        import multiprocessing
        import time
        
        # Start API server in a separate process
        api_process = multiprocessing.Process(target=run_api_server)
        api_process.start()
        
        # Give API server time to start
        time.sleep(2)
        
        # Start Dramatiq worker in a separate process
        worker_process = multiprocessing.Process(target=run_dramatiq_worker)
        worker_process.start()
        
        try:
            # Wait for both processes
            api_process.join()
            worker_process.join()
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            api_process.terminate()
            worker_process.terminate()
            api_process.join()
            worker_process.join()
