#!/bin/bash

# Social Media Lead Generation Bot Startup Script

set -e  # Exit on any error

echo "🚀 Starting Social Media Lead Generation Bot..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "Please create a .env file with your configuration."
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed!"
    exit 1
fi

# Check if PostgreSQL is running (optional check)
echo "🔍 Checking PostgreSQL connection..."
python3 setup_database.py test

if [ $? -ne 0 ]; then
    echo "❌ Database connection failed!"
    echo "Please make sure PostgreSQL is running and configured correctly."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

echo "📦 Activating virtual environment..."
source venv/bin/activate

echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Initialize database
echo "🗄️  Initializing database..."
python3 setup_database.py create

# Function to handle cleanup
cleanup() {
    echo "🛑 Shutting down..."
    kill $API_PID 2>/dev/null || true
    kill $WORKER_PID 2>/dev/null || true
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start the application based on the argument
case "${1:-both}" in
    "api")
        echo "🌐 Starting API server only..."
        python3 app.py api
        ;;
    "worker")
        echo "⚙️  Starting Dramatiq worker only..."
        python3 app.py worker
        ;;
    "both"|"")
        echo "🌐 Starting API server..."
        python3 app.py api &
        API_PID=$!
        
        sleep 3
        
        echo "⚙️  Starting Dramatiq worker..."
        python3 app.py worker &
        WORKER_PID=$!
        
        echo "✅ Both API server and worker are running!"
        echo "📊 API server: http://localhost:8000"
        echo "📊 API docs: http://localhost:8000/docs"
        echo "📊 Health check: http://localhost:8000/health"
        echo ""
        echo "Press Ctrl+C to stop both services..."
        
        # Wait for both processes
        wait $API_PID
        wait $WORKER_PID
        ;;
    *)
        echo "Usage: $0 [api|worker|both]"
        echo "  api    - Start only the API server"
        echo "  worker - Start only the Dramatiq worker"
        echo "  both   - Start both API server and worker (default)"
        exit 1
        ;;
esac
