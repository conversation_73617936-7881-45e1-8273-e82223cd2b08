import os
import json
import requests
from bs4 import BeautifulSoup
from openai import OpenAI
from tavily import TavilyClient
import re
from dotenv import load_dotenv
from logger_config import setup_logger

load_dotenv()
logger = setup_logger()

class SocialMediaPipeline:
    def __init__(self, tavily_api_key=None, openai_api_key=None):
        """Initialize the pipeline with API keys."""
        logger.info("Initializing SocialMediaPipeline")
        self.tavily_api_key = tavily_api_key or os.environ.get("TAVILY_API_KEY")
        self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY_v2")
    
        if not self.tavily_api_key:
            logger.error("Tavily API key is missing")
            raise ValueError("Tavily API key is required")
        if not self.openai_api_key:
            logger.error("OpenAI API key is missing")
            raise ValueError("OpenAI API key is required")
        
        self.tavily_client = TavilyClient(api_key=self.tavily_api_key)
        self.openai_client = OpenAI(api_key=self.openai_api_key)
        logger.info("Pipeline initialized successfully")
    
    def scrape_website_content(self, company_url):
        """Scrape raw website content for processing with OpenAI."""
        logger.info(f"Scraping website content from: {company_url}")
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(company_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract title
            title = ""
            title_tag = soup.find('title')
            if title_tag and title_tag.text:
                title = title_tag.text.strip()
            
            # Extract meta description
            meta_description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if not meta_desc:
                meta_desc = soup.find('meta', attrs={'property': 'og:description'})
            if meta_desc and meta_desc.get('content'):
                meta_description = meta_desc.get('content').strip()
            
            # Extract text content from paragraphs and headers
            text_content = []
            for tag in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'li']):
                if tag.text.strip():
                    text_content.append(tag.text.strip())
            
            # Combine content with priority to meta information
            website_content = {
                'url': company_url,
                'title': title,
                'meta_description': meta_description,
                'text_content': ' '.join(text_content[:100])  # Limit text to avoid token limits
            }
            
            logger.info(f"Successfully scraped content from {company_url}")
            return website_content
            
        except Exception as e:
            logger.error(f"Error scraping {company_url}: {str(e)}")
            return None
    
    def extract_company_info_with_llm(self, website_content, company_goals=None):
        """Use OpenAI to extract structured company information from website content or goals."""
        logger.info("Extracting company information using LLM")
        if not website_content and not company_goals:
            logger.warning("No website content or company goals provided")
            return None
        
        prompt = f"""
        Extract key business information from this company information.
        
        {'Website Content:' if website_content else 'Company Goals:'}
        {website_content['text_content'] if website_content else company_goals}
        
        Please extract and format the following information in JSON format:
        
        1. name: The company name
        2. description: A 1-2 sentence description of what the company does
        3. keywords: 5-10 keywords relevant to the company's business
        4. industry: The primary industry or business sector
        5. services: List of main products/services offered
        6. location: Geographic focus or headquarters location if mentioned
        7. target_audience: The company's target customers or users
        8. country: The primary country of operation
        
        Format the response as valid JSON.

        DO NOT ADD ANYTHING ELSE TO THE JSON RESPONSE.

        NOTE: MAKE SURE YOUR ENTRIED RESPONSE DO NOT EXCEED 350 CHARACTERS IN TOTAL
        """
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an AI assistant specialized in extracting business information. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ]
            )
            
            # Parse the JSON response
            company_info = json.loads(response.choices[0].message.content)
            logger.info("Successfully extracted company information")
            return company_info
            
        except Exception as e:
            logger.error(f"Error extracting company info with LLM: {str(e)}")
            return None
    
    def search_telegram_groups(self, company_info):
        """Search for telegram groups using Tavily API."""
        logger.info("Searching for telegram groups")
        if not company_info:
            logger.warning("No company info provided for telegram group search")
            return []
        
        # Construct search query using company info
        query = f"telegram groups {company_info.get('industry', '')} {company_info.get('services', [])} {company_info.get('country', '')}"
        logger.debug(f"Search query: {query}")
        
        try:
            # Search with Tavily
            search_results = self.tavily_client.search(query, max_results=4)
            
            telegram_groups = []
            non_telegram_urls = []
            
            # Process results
            for result in search_results.get('results', []):
                url = result.get('url', '')
                if 't.me/' in url or 'telegram.me/' in url:
                    telegram_groups.append({
                        'url': url,
                        'title': result.get('title', ''),
                        'description': result.get('content', '')
                    })
                else:
                    non_telegram_urls.append(url)
            
            logger.info(f"Found {len(telegram_groups)} direct telegram groups")
            
            # Scrape non-telegram websites for telegram links
            for url in non_telegram_urls:
                try:
                    logger.debug(f"Scraping {url} for telegram links")
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    response = requests.get(url, headers=headers, timeout=10)
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Find all links
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if 't.me/' in href or 'telegram.me/' in href:
                            telegram_groups.append({
                                'url': href,
                                'title': link.text.strip() or 'Telegram Group',
                                'description': 'Found on ' + url
                            })
                except Exception as e:
                    logger.error(f"Error scraping {url} for telegram links: {str(e)}")
                    continue
            
            logger.info(f"Total telegram groups found: {len(telegram_groups)}")
            return telegram_groups
            
        except Exception as e:
            logger.error(f"Error searching for telegram groups: {str(e)}")
            return []

    def search_whatsapp_groups(self, company_info):
        """Search for WhatsApp groups using Tavily API."""
        logger.info("Searching for WhatsApp groups")
        if not company_info:
            logger.warning("No company info provided for WhatsApp group search")
            return []
        
        # Construct search query using company info
        query = f"whatsapp groups {company_info.get('industry', '')} {company_info.get('services', [])} {company_info.get('country', '')}"
        logger.debug(f"Search query: {query}")
        
        try:
            # Search with Tavily
            search_results = self.tavily_client.search(query, max_results=4)
            
            whatsapp_groups = []
            non_whatsapp_urls = []
            
            # Process results
            for result in search_results.get('results', []):
                url = result.get('url', '')
                content = result.get('content', '')
                
                # Look for WhatsApp group links in the content
                whatsapp_links = re.findall(r'https?://(?:chat\.whatsapp\.com|wa\.me)/[a-zA-Z0-9]+', content)
                if whatsapp_links:
                    for link in whatsapp_links:
                        whatsapp_groups.append({
                            'url': link,
                            'title': result.get('title', ''),
                            'description': result.get('content', '')
                        })
                else:
                    non_whatsapp_urls.append(url)
            
            logger.info(f"Found {len(whatsapp_groups)} direct WhatsApp groups")
            
            # Scrape non-WhatsApp websites for WhatsApp links
            for url in non_whatsapp_urls:
                try:
                    logger.debug(f"Scraping {url} for WhatsApp links")
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    response = requests.get(url, headers=headers, timeout=10)
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Find all links
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if 'chat.whatsapp.com' in href or 'wa.me' in href:
                            whatsapp_groups.append({
                                'url': href,
                                'title': link.text.strip() or 'WhatsApp Group',
                                'description': 'Found on ' + url
                            })
                except Exception as e:
                    logger.error(f"Error scraping {url} for WhatsApp links: {str(e)}")
                    continue
            
            logger.info(f"Total WhatsApp groups found: {len(whatsapp_groups)}")
            return whatsapp_groups
            
        except Exception as e:
            logger.error(f"Error searching for WhatsApp groups: {str(e)}")
            return []

    def search_twitter_links(self, company_info):
        """Search for Twitter usernames and construct profile URLs."""
        logger.info("Searching for Twitter usernames")
        if not company_info:
            logger.warning("No company info provided for Twitter search")
            return []
        
        # Construct search query using company info
        company_name = company_info.get('name', '').lower()
        query = f"twitter username {company_name} {company_info.get('industry', '')} {company_info.get('country', '')}"
        logger.debug(f"Search query: {query}")
        
        try:
            # Search with Tavily
            search_results = self.tavily_client.search(query, max_results=4)
            
            twitter_profiles = []
            seen_usernames = set()
            
            # Process results
            for result in search_results.get('results', []):
                content = result.get('content', '')
                title = result.get('title', '')
                
                # Look for Twitter usernames in content
                # Pattern for @username or twitter.com/username
                username_patterns = [
                    r'@([a-zA-Z0-9_]{1,15})',  # @username
                    r'(?:twitter\.com|x\.com)/([a-zA-Z0-9_]{1,15})',  # twitter.com/username
                    r'Twitter: @([a-zA-Z0-9_]{1,15})',  # Twitter: @username
                    r'Twitter handle: @([a-zA-Z0-9_]{1,15})'  # Twitter handle: @username
                ]
                
                for pattern in username_patterns:
                    usernames = re.findall(pattern, content)
                    for username in usernames:
                        username = username.lower()  # Normalize username to lowercase
                        if username not in seen_usernames:
                            seen_usernames.add(username)
                            # Construct Twitter profile URL
                            profile_url = f"https://twitter.com/{username}"
                            twitter_profiles.append({
                                'url': profile_url,
                                'username': username,
                                'title': title,
                                'description': f"Twitter profile for {company_name}",
                                'type': 'profile'
                            })
            
            # If no usernames found, try to extract from company name
            if not twitter_profiles and company_name:
                # Try common variations of company name
                possible_usernames = [
                    company_name.replace(' ', ''),  # Remove spaces
                    company_name.replace(' ', '_'),  # Replace spaces with underscores
                    ''.join(word[0] for word in company_name.split()),  # Acronym
                    company_name.split()[0]  # First word
                ]
                
                for username in possible_usernames:
                    username = username.lower()
                    if username not in seen_usernames and len(username) <= 15:
                        seen_usernames.add(username)
                        profile_url = f"https://twitter.com/{username}"
                        twitter_profiles.append({
                            'url': profile_url,
                            'username': username,
                            'title': f"Possible Twitter profile for {company_name}",
                            'description': f"Constructed Twitter profile URL based on company name",
                            'type': 'profile'
                        })
            
            logger.info(f"Found {len(twitter_profiles)} Twitter profiles")
            return twitter_profiles
            
        except Exception as e:
            logger.error(f"Error searching for Twitter profiles: {str(e)}")
            return []
    
    def run_pipeline(self, company_url=None, company_goals=None):
        """Run the complete pipeline."""
        logger.info("Starting pipeline execution")
        if not company_url and not company_goals:
            logger.error("Neither company_url nor company_goals provided")
            raise ValueError("Either company_url or company_goals must be provided")
        
        # Step 1: Scrape website content if URL provided
        website_content = None
        if company_url:
            logger.info(f"Processing company URL: {company_url}")
            website_content = self.scrape_website_content(company_url)
        
        # Step 2: Extract company info
        company_info = self.extract_company_info_with_llm(website_content, company_goals)
        
        # Step 3: Search for social media links
        telegram_groups = self.search_telegram_groups(company_info)
        whatsapp_groups = self.search_whatsapp_groups(company_info)
        twitter_links = self.search_twitter_links(company_info)
        
        logger.info("Pipeline execution completed")
        return {
            "company_info": company_info,
            "telegram_groups": telegram_groups,
            "whatsapp_groups": whatsapp_groups,
            "twitter_links": twitter_links
        }

# Example usage
if __name__ == "__main__":
    logger.info("Starting main execution")
    # Set your API keys in environment variables or pass directly
    pipeline = SocialMediaPipeline(
        tavily_api_key=os.environ.get("TAVILY_API_KEY"),
        openai_api_key=os.environ.get("OPENAI_API_KEY_v2")



    )
    
    # Run with company URL
    logger.info("Running pipeline with company URL")
    url_results = pipeline.run_pipeline(
        company_url="https://www.quidax.io/",
        company_goals="Quidax is a Nigerian-based cryptocurrency exchange that provides users with a platform to buy, sell, and trade cryptocurrencies."
    )
    
    # Run with company goals
    logger.info("Running pipeline with company goals")
    goals_results = {"telegram_groups": [], "whatsapp_groups": [], "twitter_links": []}
    
    # Combine results and remove duplicates based on URL
    logger.info("Combining results from both searches")
    combined_results = {
        "telegram_groups": [],
        "whatsapp_groups": [],
        "twitter_links": []
    }
    
    # Process each social media type
    for media_type in combined_results.keys():
        seen_urls = set()
        for result in url_results[media_type] + goals_results[media_type]:
            if result["url"] not in seen_urls:
                seen_urls.add(result["url"])
                combined_results[media_type].append(result)
    
    logger.info(f"Found {len(combined_results['telegram_groups'])} unique telegram groups")
    logger.info(f"Found {len(combined_results['whatsapp_groups'])} unique WhatsApp groups")
    logger.info(f"Found {len(combined_results['twitter_links'])} unique Twitter links")
    
    print("\nCombined Results from both URL and Goals searches:")
    #save rsult as josn
    with open('results.json', 'w') as f:
        json.dump(combined_results, f, indent=2)
    logger.info("Main execution completed")