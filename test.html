<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceForge AI Studio - Create Your Talking Avatar</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            padding: 0 2rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .credits {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        /* Progress Steps */
        .progress-container {
            margin-top: 70px;
            padding: 2rem;
            background: rgba(15, 15, 35, 0.8);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            z-index: 1;
        }

        .progress-line {
            position: absolute;
            top: 25px;
            left: 50px;
            height: 2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            transition: width 0.5s ease;
            z-index: 2;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 3;
        }

        .step-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .step.active .step-circle {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-color: #4ecdc4;
            transform: scale(1.1);
        }

        .step.completed .step-circle {
            background: #28a745;
            border-color: #28a745;
        }

        .step-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .step-desc {
            font-size: 0.7rem;
            color: #aaa;
            max-width: 120px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            min-height: calc(100vh - 200px);
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            gap: 2rem;
        }

        /* Workflow Panel */
        .workflow-panel {
            flex: 2;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(20px);
        }

        .step-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .step-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-header {
            margin-bottom: 2rem;
        }

        .step-number {
            color: #4ecdc4;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-title-main {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .step-description {
            font-size: 1.1rem;
            color: #ccc;
            line-height: 1.6;
        }

        /* Upload Areas */
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 3rem;
            text-align: center;
            margin: 2rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .upload-area:hover {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.05);
        }

        .upload-area.has-file {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: #4ecdc4;
            margin-bottom: 1rem;
        }

        .upload-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin: 1rem auto;
            display: none;
        }

        /* Voice Recorder */
        .voice-recorder {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }

        .record-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem auto;
            display: block;
        }

        .record-button.recording {
            background: #dc3545;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .audio-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        /* Text Editor */
        .text-editor {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .editor-toolbar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-btn {
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .editor-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .prompt-suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .suggestion-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        /* Form Controls */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        /* Buttons */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Preview Panel */
        .preview-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(20px);
            position: sticky;
            top: 2rem;
            height: fit-content;
        }

        .preview-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .preview-avatar {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .preview-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .preview-status {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #666;
        }

        .status-icon.completed {
            background: #28a745;
        }

        .status-icon.active {
            background: #4ecdc4;
            animation: pulse 2s infinite;
        }

        /* Final Video Preview */
        .video-preview {
            background: #000;
            border-radius: 12px;
            aspect-ratio: 16/9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2rem 0;
            position: relative;
        }

        .play-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.5rem;
            color: #333;
        }

        /* Loading States */
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(78, 205, 196, 0.3);
            border-top: 3px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
            }
            
            .preview-panel {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .progress-steps {
                flex-wrap: wrap;
                gap: 1rem;
            }
            
            .step {
                flex: 1 1 auto;
            }
            
            .progress-steps::before,
            .progress-line {
                display: none;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-video"></i>
                FaceForge AI Studio
            </div>
            <div class="user-profile">
                <div class="credits">
                    <i class="fas fa-coins"></i> 25 Credits
                </div>
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Progress Steps -->
    <div class="progress-container">
        <div class="progress-steps">
            <div class="progress-line" style="width: 0%"></div>
            
            <div class="step active" data-step="1">
                <div class="step-circle">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="step-title">Upload Selfie</div>
                <div class="step-desc">Upload your photo</div>
            </div>
            
            <div class="step" data-step="2">
                <div class="step-circle">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="step-title">Customize Look</div>
                <div class="step-desc">Edit your appearance</div>
            </div>
            
            <div class="step" data-step="3">
                <div class="step-circle">
                    <i class="fas fa-microphone"></i>
                </div>
                <div class="step-title">Add Voice</div>
                <div class="step-desc">Record audio clip</div>
            </div>
            
            <div class="step" data-step="4">
                <div class="step-circle">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="step-title">Type Message</div>
                <div class="step-desc">Write your script</div>
            </div>
            
            <div class="step" data-step="5">
                <div class="step-circle">
                    <i class="fas fa-play"></i>
                </div>
                <div class="step-title">Create Video</div>
                <div class="step-desc">Generate avatar</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Workflow Panel -->
        <div class="workflow-panel">
            <!-- Step 1: Upload Selfie -->
            <div class="step-content active" data-step="1">
                <div class="step-header">
                    <div class="step-number">STEP 1</div>
                    <h2 class="step-title-main">Upload Your Selfie</h2>
                    <p class="step-description">
                        Start by uploading a clear photo of yourself. For best results, use a well-lit selfie where your face is clearly visible and centered in the frame.
                    </p>
                </div>

                <div class="upload-area" id="selfie-upload">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3>Drop your selfie here or click to upload</h3>
                    <p>Supports JPG, PNG, WebP up to 10MB</p>
                    <img class="upload-preview" id="selfie-preview" alt="Uploaded selfie" />
                </div>

                <div class="form-group">
                    <label class="form-label">Photo Guidelines</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                        <div class="suggestion-card">
                            <i class="fas fa-check-circle" style="color: #28a745; margin-bottom: 0.5rem;"></i>
                            <div><strong>Good lighting</strong></div>
                            <small>Natural light works best</small>
                        </div>
                        <div class="suggestion-card">
                            <i class="fas fa-check-circle" style="color: #28a745; margin-bottom: 0.5rem;"></i>
                            <div><strong>Face centered</strong></div>
                            <small>Your face should be the focus</small>
                        </div>
                        <div class="suggestion-card">
                            <i class="fas fa-check-circle" style="color: #28a745; margin-bottom: 0.5rem;"></i>
                            <div><strong>Clear quality</strong></div>
                            <small>High resolution preferred</small>
                        </div>
                    </div>
                </div>

                <div class="btn-navigation">
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-primary" id="next-step-1" disabled>
                        Next Step <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- Step 2: Customize Look -->
            <div class="step-content" data-step="2">
                <div class="step-header">
                    <div class="step-number">STEP 2</div>
                    <h2 class="step-title-main">Customize Your Look</h2>
                    <p class="step-description">
                        Use AI-powered visual editing to transform your appearance. Simply describe the changes you want and watch your selfie transform in real-time.
                    </p>
                </div>

                <div class="text-editor">
                    <label class="form-label">Describe Your Desired Changes</label>
                    <textarea class="form-textarea" id="customization-prompt" placeholder="Example: Change my hair color to blonde, add subtle makeup, give me a professional business look with a navy suit..."></textarea>
                    
                    <div class="editor-toolbar">
                        <button class="editor-btn" onclick="addPromptText('Change hair color to ')">
                            <i class="fas fa-cut"></i> Hair Color
                        </button>
                        <button class="editor-btn" onclick="addPromptText('Add makeup: ')">
                            <i class="fas fa-palette"></i> Makeup
                        </button>
                        <button class="editor-btn" onclick="addPromptText('Add accessories: ')">
                            <i class="fas fa-glasses"></i> Accessories
                        </button>
                        <button class="editor-btn" onclick="addPromptText('Change outfit to ')">
                            <i class="fas fa-tshirt"></i> Outfit
                        </button>
                    </div>
                </div>

                <div class="prompt-suggestions">
                    <div class="suggestion-card" onclick="setPrompt('Change my hair to platinum blonde, add subtle eye makeup and glossy lips')">
                        <strong>Glamorous Look</strong>
                        <small>Platinum hair, eye makeup, glossy lips</small>
                    </div>
                    <div class="suggestion-card" onclick="setPrompt('Give me a professional business appearance with a navy suit and natural makeup')">
                        <strong>Professional</strong>
                        <small>Business suit, natural makeup</small>
                    </div>
                    <div class="suggestion-card" onclick="setPrompt('Add colorful hair, bold makeup, and trendy accessories for a vibrant look')">
                        <strong>Creative & Bold</strong>
                        <small>Colorful hair, bold makeup, trendy style</small>
                    </div>
                    <div class="suggestion-card" onclick="setPrompt('Casual style with natural makeup and a comfortable sweater')">
                        <strong>Casual & Natural</strong>
                        <small>Natural look, comfortable outfit</small>
                    </div>
                </div>

                <button class="btn btn-primary" id="apply-customization" style="width: 100%; margin-top: 1rem;">
                    <i class="fas fa-magic"></i> Apply Customization
                </button>

                <div class="btn-navigation">
                    <button class="btn btn-secondary" onclick="previousStep()">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-primary" id="next-step-2" disabled>
                        Next Step <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- Step 3: Add Voice -->
            <div class="step-content" data-step="3">
                <div class="step-header">
                    <div class="step-number">STEP 3</div>
                    <h2 class="step-title-main">Add Your Voice</h2>
                    <p class="step-description">
                        Record a 10-30 second audio clip of your voice. Our AI will analyze your vocal patterns and create a high-quality voice clone for your avatar.
                    </p>
                </div>

                <div class="voice-recorder">
                    <h3>Voice Recording</h3>
                    <p>Click the button below to start recording</p>
                    
                    <button class="record-button" id="record-btn">
                        <i class="fas fa-microphone"></i>
                    </button>
                    
                    <div id="recording-status" style="margin-top: 1rem; display: none;">
                        <div class="spinner"></div>
                        <p>Recording... <span id="timer">00:00</span></p>
                    </div>
                    
                    <div class="audio-controls" id="audio-controls" style="display: none;">
                        <button class="btn btn-secondary" id="play-recording">
                            <i class="fas fa-play"></i> Play
                        </button>
                        <button class="btn btn-secondary" id="re-record">
                            <i class="fas fa-redo"></i> Re-record
                        </button>
                    </div>
                    
                    <audio id="recorded-audio" style="display: none;" controls></audio>
                </div>

                <div class="form-group">
                    <label class="form-label">Recording Tips</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                        <div class="suggestion-card">
                            <i class="fas fa-volume-up" style="color: #4ecdc4; margin-bottom: 0.5rem;"></i>
                            <div><strong>Speak Clearly</strong></div>
                            <small>Use your natural speaking voice</small>
                        </div>
                        <div class="suggestion-card">
                            <i class="fas fa-clock" style="color: #4ecdc4; margin-bottom: 0.5rem;"></i>
                            <div><strong>10-30 Seconds</strong></div>
                            <small>Enough time for voice analysis</small>
                        </div>
                        <div class="suggestion-card">
                            <i class="fas fa-microphone-slash" style="color: #4ecdc4; margin-bottom: 0.5rem;"></i>
                            <div><strong>Quiet Environment</strong></div>
                            <small>Minimal background noise</small>
                        </div>
                    </div>
                </div>

                <div class="btn-navigation">
                    <button class="btn btn-secondary" onclick="previousStep()">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-primary" id="next-step-3" disabled>
                        Next Step <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- Step 4: Type Message -->
            <div class="step-content" data-step="4">
                <div class="step-header">
                    <div class="step-number">STEP 4</div>
                    <h2 class="step-title-main">Type What You Want to Say</h2>
                    <p class="step-description">
                        Write the script for your talking avatar. This can be anything from a personal message to a business pitch, joke, or creative content.
                    </p>
                </div>

                <div class="form-group">
                    <label class="form-label">Your Message Script</label>
                    <textarea class="form-textarea" id="message-script" placeholder="Hello! I'm excited to show you this amazing AI avatar technology. This could be a greeting, a pitch, a joke, or any message you want to share..." style="min-height: 150px;"></textarea>
                    <small style="color: #888; margin-top: 0.5rem; display: block;">
                        <span id="char-count">0</span> characters • Recommended: 100-500 characters for best results
                    </small>
                </div>

                <div class="form-group">
                    <label class="form-label">Message Type</label>
                    <select class="form-input" id="message-type">
                        <option value="greeting">Personal Greeting</option>
                        <option value="business">Business Pitch</option>
                        <option value="joke">Joke/Comedy</option>
                        <option value="educational">Educational Content</option>
                        <option value="announcement">Announcement</option>
                        <option value="creative">Creative/Artistic</option>
                        <option value="custom">Custom Message</option>
                    </select>
                </div>

                <div class="prompt-suggestions">
                    <div class="suggestion-card" onclick="setScript('Hello! Welcome to my channel. I'm excited to share some amazing content with you today. Don't forget to subscribe and hit the notification bell!')">
                        <strong>YouTube Intro</strong>
                        <small>Channel welcome message</small>
                    </div>
                    <div class="suggestion-card" onclick="setScript('Hi there! I'm reaching out to introduce our innovative solution that can transform your business. Let's schedule a quick 15-minute call to discuss how we can help you achieve your goals.')">
                        <strong>Business Pitch</strong>
                        <small>Professional introduction</small>
                    </div>
                    <div class="suggestion-card" onclick="setScript('Why did the AI cross the road? To get to the other dataset! But seriously, isn't technology amazing? I'm literally a talking avatar right now!')">
                        <strong>Tech Humor</strong>
                        <small>Light-hearted joke</small>
                    </div>
                    <div class="suggestion-card" onclick="setScript('Thank you for joining our presentation today. I'm excited to walk you through our latest findings and share some key insights that could impact our industry.')">
                        <strong>Presentation</strong>
                        <small>Meeting opener</small>
                    </div>
                </div>

                <div class="btn-navigation">
                    <button class="btn btn-secondary" onclick="previousStep()">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-primary" id="next-step-4" disabled>
                        Next Step <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- Step 5: Create Video -->
            <div class="step-content" data-step="5">
                <div class="step-header">
                    <div class="step-number">STEP 5</div>
                    <h2 class="step-title-main">Create Your Talking Video</h2>
                    <p class="step-description">
                        Now we'll combine your customized selfie, cloned voice, and script to create your dynamic talking avatar video. This process typically takes 2-3 minutes.
                    </p>
                </div>

                <div class="form-group">
                    <label class="form-label">Video Settings</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div>
                            <label class="form-label">Video Quality</label>
                            <select class="form-input" id="video-quality">
                                <option value="hd">HD (1280x720)</option>
                                <option value="fhd" selected>Full HD (1920x1080)</option>
                                <option value="4k">4K (3840x2160) - Pro Only</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label">Background</label>
                            <select class="form-input" id="background-type">
                                <option value="original">Keep Original</option>
                                <option value="blur">Blur Background</option>
                                <option value="studio">Studio Background</option>
                                <option value="green">Green Screen</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="video-preview" id="video-preview">
                    <button class="play-button" id="preview-play" style="display: none;">
                        <i class="fas fa-play"></i>
                    </button>
                    <div id="video-placeholder">
                        <i class="fas fa-video" style="font-size: 3rem; color: #666;"></i>
                        <p style="margin-top: 1rem; color: #888;">Your video will appear here</p>
                    </div>
                </div>

                <div class="loading" id="video-generation-loading">
                    <div class="spinner"></div>
                    <h3>Creating your talking avatar...</h3>
                    <p>This may take 2-3 minutes</p>
                    <div style="margin-top: 1rem;">
                        <div style="background: rgba(255,255,255,0.1); height: 6px; border-radius: 3px; overflow: hidden;">
                            <div id="progress-bar" style="height: 100%; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); width: 0%; transition: width 0.5s ease;"></div>
                        </div>
                        <p id="progress-text" style="margin-top: 0.5rem; font-size: 0.9rem;">Initializing...</p>
                    </div>
                </div>

                <button class="btn btn-primary" id="generate-video" style="width: 100%; margin-top: 1rem;">
                    <i class="fas fa-magic"></i> Generate Talking Avatar Video
                </button>

                <div id="video-complete" style="display: none; text-align: center; margin-top: 2rem;">
                    <h3 style="color: #28a745; margin-bottom: 1rem;">
                        <i class="fas fa-check-circle"></i> Video Generated Successfully!
                    </h3>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <button class="btn btn-primary">
                            <i class="fas fa-download"></i> Download Video
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-share"></i> Share Video
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-edit"></i> Make Another
                        </button>
                    </div>
                </div>

                <div class="btn-navigation">
                    <button class="btn btn-secondary" onclick="previousStep()">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-secondary" onclick="startOver()">
                        <i class="fas fa-redo"></i> Start Over
                    </button>
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <div class="preview-header">
                <h3>Live Preview</h3>
                <p>Watch your avatar come to life</p>
            </div>

            <div class="preview-avatar" id="preview-avatar">
                <i class="fas fa-user"></i>
            </div>

            <div class="preview-status">
                <div class="status-item">
                    <div class="status-icon" id="status-selfie"></div>
                    <span>Selfie Uploaded</span>
                </div>
                <div class="status-item">
                    <div class="status-icon" id="status-customization"></div>
                    <span>Appearance Customized</span>
                </div>
                <div class="status-item">
                    <div class="status-icon" id="status-voice"></div>
                    <span>Voice Recorded</span>
                </div>
                <div class="status-item">
                    <div class="status-icon" id="status-script"></div>
                    <span>Script Written</span>
                </div>
                <div class="status-item">
                    <div class="status-icon" id="status-video"></div>
                    <span>Video Generated</span>
                </div>
            </div>

            <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                <h4>Credits Required</h4>
                <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                    <span>Video Generation:</span>
                    <span>5 credits</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 0.5rem;">
                    <span>Voice Cloning:</span>
                    <span>3 credits</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 0.5rem; font-weight: bold; padding-top: 0.5rem; border-top: 1px solid rgba(255,255,255,0.1);">
                    <span>Total:</span>
                    <span>8 credits</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentStep = 1;
        let sessionId = null;
        let authToken = null;
        let uploadedSelfie = null;
        let customizedImage = null;
        let recordedAudio = null;
        let messageScript = '';
        let isRecording = false;
        let recordingTimer = null;
        let recordingTime = 0;
        let mediaRecorder = null;
        let audioChunks = [];
        let websocket = null;

        // API Configuration
        const API_BASE_URL = 'http://localhost:8000';

        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            try {
                // Mock authentication for demo
                await authenticateUser();
                
                // Start new session
                await startSession();
                
                // Setup UI
                updateProgressLine();
                setupEventListeners();
                updateCharCount();
                
                // Setup WebSocket connection
                setupWebSocket();
                
                console.log('FaceForge AI Studio initialized successfully');
            } catch (error) {
                console.error('Failed to initialize app:', error);
                showError('Failed to initialize application. Please refresh the page.');
            }
        }

        async function authenticateUser() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Authentication failed');
                }
                
                const data = await response.json();
                authToken = data.token;
                
                // Update UI with user info
                updateUserProfile(data.user);
                
            } catch (error) {
                console.error('Authentication error:', error);
                throw error;
            }
        }

        async function startSession() {
            try {
                const response = await fetch(`${API_BASE_URL}/session/start`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to start session');
                }
                
                const data = await response.json();
                sessionId = data.sessionId;
                currentStep = getStepNumber(data.currentStep);
                
                console.log(`Session started: ${sessionId}`);
                
            } catch (error) {
                console.error('Session start error:', error);
                throw error;
            }
        }

        function setupWebSocket() {
            if (!sessionId) return;
            
            const wsUrl = `ws://localhost:8000/ws/${sessionId}`;
            websocket = new WebSocket(wsUrl);
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'session_update') {
                    handleSessionUpdate(data);
                }
            };
            
            websocket.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
            
            websocket.onclose = function() {
                // Attempt to reconnect after 5 seconds
                setTimeout(setupWebSocket, 5000);
            };
        }

        function handleSessionUpdate(data) {
            // Update progress based on server state
            updateProgressFromServer(data);
        }

        function updateUserProfile(user) {
            const creditsElement = document.querySelector('.credits');
            if (creditsElement) {
                creditsElement.innerHTML = `<i class="fas fa-coins"></i> ${user.credits} Credits`;
            }
        }

        function getStepNumber(stepName) {
            const stepMap = {
                'upload_selfie': 1,
                'customize_look': 2,
                'add_voice': 3,
                'type_message': 4,
                'create_video': 5
            };
            return stepMap[stepName] || 1;
        }

        function showError(message) {
            // Create error notification
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-notification';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #dc3545;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                z-index: 10000;
                max-width: 300px;
            `;
            errorDiv.textContent = message;
            
            document.body.appendChild(errorDiv);
            
            // Remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        function showSuccess(message) {
            // Create success notification
            const successDiv = document.createElement('div');
            successDiv.className = 'success-notification';
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 1rem;
                border-radius: 8px;
                z-index: 10000;
                max-width: 300px;
            `;
            successDiv.textContent = message;
            
            document.body.appendChild(successDiv);
            
            // Remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }

        function setupEventListeners() {
            // File upload
            const selfieUpload = document.getElementById('selfie-upload');
            if (selfieUpload) {
                selfieUpload.addEventListener('click', handleSelfieUploadClick);
            }

            // Customization apply
            const applyCustomization = document.getElementById('apply-customization');
            if (applyCustomization) {
                applyCustomization.addEventListener('click', applyCustomization);
            }

            // Voice recording
            const recordBtn = document.getElementById('record-btn');
            const playRecording = document.getElementById('play-recording');
            const reRecord = document.getElementById('re-record');
            
            if (recordBtn) recordBtn.addEventListener('click', toggleRecording);
            if (playRecording) playRecording.addEventListener('click', playRecordingAudio);
            if (reRecord) reRecord.addEventListener('click', reRecordAudio);

            // Script typing
            const messageScript = document.getElementById('message-script');
            if (messageScript) {
                messageScript.addEventListener('input', updateCharCount);
                messageScript.addEventListener('input', checkScriptStep);
            }

            // Video generation
            const generateVideo = document.getElementById('generate-video');
            if (generateVideo) {
                generateVideo.addEventListener('click', generateTalkingVideo);
            }

            // Navigation buttons
            const nextButtons = document.querySelectorAll('[id^="next-step-"]');
            nextButtons.forEach((btn, index) => {
                btn.addEventListener('click', () => nextStep());
            });
        }

        async function handleSelfieUploadClick() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.click();
            
            input.addEventListener('change', async (e) => {
                if (e.target.files.length > 0) {
                    await handleSelfieUpload(e.target.files[0]);
                }
            });
        }

        async function handleSelfieUpload(file) {
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('sessionId', sessionId);

                // Show loading state
                const uploadArea = document.getElementById('selfie-upload');
                const originalContent = uploadArea.innerHTML;
                uploadArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <h3>Analyzing your selfie...</h3>
                    <p>Please wait while we process your image</p>
                `;

                const response = await fetch(`${API_BASE_URL}/step1/upload-selfie`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Upload failed');
                }

                const data = await response.json();
                uploadedSelfie = data.url;
                
                // Update upload area
                uploadArea.classList.add('has-file');
                uploadArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-check-circle" style="color: #28a745;"></i>
                    </div>
                    <h3>Selfie uploaded successfully!</h3>
                    <p>${file.name}</p>
                    <div style="margin-top: 1rem;">
                        <small>Face Quality Score: ${Math.round(data.faceQualityScore * 100)}%</small>
                    </div>
                `;
                
                // Update preview panel
                const previewAvatar = document.getElementById('preview-avatar');
                previewAvatar.innerHTML = `<img src="${uploadedSelfie}" alt="Uploaded selfie" />`;
                
                // Update status
                document.getElementById('status-selfie').classList.add('completed');
                document.getElementById('next-step-1').disabled = false;
                
                // Show recommendations if any
                if (data.recommendations && data.recommendations.length > 0) {
                    showSuccess('Selfie uploaded! ' + data.recommendations.join(', '));
                } else {
                    showSuccess('Perfect selfie uploaded!');
                }
                
            } catch (error) {
                console.error('Selfie upload error:', error);
                showError(error.message);
                
                // Reset upload area
                const uploadArea = document.getElementById('selfie-upload');
                uploadArea.classList.remove('has-file');
                uploadArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                    </div>
                    <h3>Upload failed</h3>
                    <p>Click to try again</p>
                `;
            }
        }

        async function applyCustomization() {
            try {
                const prompt = document.getElementById('customization-prompt').value.trim();
                if (!prompt) {
                    showError('Please describe the changes you want to make.');
                    return;
                }

                const btn = document.getElementById('apply-customization');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Applying Changes...';

                const response = await fetch(`${API_BASE_URL}/step2/customize-look`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId,
                        customizationPrompt: prompt,
                        styleIntensity: 0.7
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Customization failed');
                }

                const data = await response.json();
                
                // Poll for completion
                pollCustomizationStatus(data.jobId, btn, originalText);
                showSuccess('Customization started! This may take up to 30 seconds.');
                
            } catch (error) {
                console.error('Customization error:', error);
                showError(error.message);
                
                const btn = document.getElementById('apply-customization');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> Apply Customization';
            }
        }

        async function pollCustomizationStatus(jobId, btn, originalText) {
            try {
                const response = await fetch(
                    `${API_BASE_URL}/step2/customization-status/${jobId}?sessionId=${sessionId}`,
                    {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    }
                );

                const data = await response.json();
                
                if (data.status === 'completed') {
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                    
                    // Update status
                    document.getElementById('status-customization').classList.add('completed');
                    document.getElementById('next-step-2').disabled = false;
                    
                    showSuccess('Customization completed successfully!');
                } else {
                    // Continue polling
                    setTimeout(() => pollCustomizationStatus(jobId, btn, originalText), 3000);
                }
                
            } catch (error) {
                console.error('Status polling error:', error);
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                stopRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                isRecording = true;
                recordingTime = 0;
                
                const recordBtn = document.getElementById('record-btn');
                const recordingStatus = document.getElementById('recording-status');
                
                recordBtn.classList.add('recording');
                recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
                recordingStatus.style.display = 'block';
                
                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    await processRecordedAudio(audioBlob);
                };
                
                mediaRecorder.start();
                
                // Start timer
                recordingTimer = setInterval(() => {
                    recordingTime++;
                    const minutes = Math.floor(recordingTime / 60);
                    const seconds = recordingTime % 60;
                    document.getElementById('timer').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // Auto-stop after 30 seconds
                    if (recordingTime >= 30) {
                        stopRecording();
                    }
                }, 1000);
                
            } catch (error) {
                console.error('Recording error:', error);
                showError('Failed to access microphone. Please check permissions.');
            }
        }

        function stopRecording() {
            if (!isRecording) return;
            
            isRecording = false;
            clearInterval(recordingTimer);
            
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }
            
            const recordBtn = document.getElementById('record-btn');
            const recordingStatus = document.getElementById('recording-status');
            
            recordBtn.classList.remove('recording');
            recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            recordingStatus.style.display = 'none';
        }

        async function processRecordedAudio(audioBlob) {
            try {
                // Convert audio blob to base64
                const arrayBuffer = await audioBlob.arrayBuffer();
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
                
                // Send to server for voice cloning
                const response = await fetch(`${API_BASE_URL}/step3/clone-voice`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId,
                        audioData: base64Audio,
                        durationSeconds: recordingTime,
                        sampleRate: 44100,
                        format: 'wav'
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Voice processing failed');
                }

                const data = await response.json();
                recordedAudio = data.voiceId;
                
                // Show audio controls
                const audioControls = document.getElementById('audio-controls');
                audioControls.style.display = 'flex';
                
                // Poll for voice cloning completion
                pollVoiceStatus(data.voiceId);
                showSuccess('Voice recorded! Processing your voice clone...');
                
            } catch (error) {
                console.error('Audio processing error:', error);
                showError(error.message);
            }
        }

        async function pollVoiceStatus(voiceId) {
            try {
                const response = await fetch(
                    `${API_BASE_URL}/step3/voice-status/${voiceId}?sessionId=${sessionId}`,
                    {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    }
                );

                const data = await response.json();
                
                if (data.status === 'completed' && data.cloneReady) {
                    // Update status
                    document.getElementById('status-voice').classList.add('completed');
                    document.getElementById('next-step-3').disabled = false;
                    
                    showSuccess(`Voice clone ready! Quality score: ${Math.round(data.qualityScore * 100)}%`);
                } else {
                    // Continue polling
                    setTimeout(() => pollVoiceStatus(voiceId), 3000);
                }
                
            } catch (error) {
                console.error('Voice status polling error:', error);
            }
        }

        function playRecordingAudio() {
            // In a real implementation, this would play the recorded audio
            showSuccess('Playing recorded audio...');
        }

        function reRecordAudio() {
            document.getElementById('audio-controls').style.display = 'none';
            document.getElementById('status-voice').classList.remove('completed');
            document.getElementById('next-step-3').disabled = true;
            recordedAudio = null;
            audioChunks = [];
        }

        function updateCharCount() {
            const textarea = document.getElementById('message-script');
            const charCount = document.getElementById('char-count');
            
            if (!textarea || !charCount) return;
            
            const count = textarea.value.length;
            charCount.textContent = count;
            
            // Color coding for character count
            if (count < 50) {
                charCount.style.color = '#ff6b6b';
            } else if (count <= 500) {
                charCount.style.color = '#28a745';
            } else {
                charCount.style.color = '#ffc107';
            }
        }

        async function checkScriptStep() {
            const script = document.getElementById('message-script').value;
            const nextBtn = document.getElementById('next-step-4');
            
            if (script.trim().length >= 10) {
                // Save script to server
                try {
                    const response = await fetch(`${API_BASE_URL}/step4/save-script`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            sessionId,
                            script: script.trim(),
                            messageType: document.getElementById('message-type')?.value || 'custom',
                            emotionTone: 'neutral'
                        })
                    });

                    if (response.ok) {
                        document.getElementById('status-script').classList.add('completed');
                        nextBtn.disabled = false;
                        messageScript = script;
                    }
                } catch (error) {
                    console.error('Script save error:', error);
                }
            } else {
                document.getElementById('status-script').classList.remove('completed');
                nextBtn.disabled = true;
            }
        }

        async function generateTalkingVideo() {
            try {
                const generateBtn = document.getElementById('generate-video');
                const loading = document.getElementById('video-generation-loading');
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');
                const videoComplete = document.getElementById('video-complete');
                
                generateBtn.style.display = 'none';
                loading.classList.add('active');
                
                // Start video generation
                const response = await fetch(`${API_BASE_URL}/step5/generate-video`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sessionId,
                        videoQuality: document.getElementById('video-quality')?.value || 'fhd',
                        backgroundType: document.getElementById('background-type')?.value || 'original'
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Video generation failed');
                }

                const data = await response.json();
                
                // Simulate progress and poll for completion
                pollVideoGeneration(data.jobId, progressBar, progressText, loading, videoComplete);
                
            } catch (error) {
                console.error('Video generation error:', error);
                showError(error.message);
                
                const generateBtn = document.getElementById('generate-video');
                const loading = document.getElementById('video-generation-loading');
                generateBtn.style.display = 'block';
                loading.classList.remove('active');
            }
        }

        async function pollVideoGeneration(jobId, progressBar, progressText, loading, videoComplete) {
            const steps = [
                { progress: 10, text: 'Processing selfie...' },
                { progress: 25, text: 'Applying customizations...' },
                { progress: 40, text: 'Analyzing voice patterns...' },
                { progress: 60, text: 'Generating lip sync...' },
                { progress: 80, text: 'Rendering video...' },
                { progress: 100, text: 'Finalizing...' }
            ];
            
            let stepIndex = 0;
            
            const progressInterval = setInterval(async () => {
                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];
                    progressBar.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    stepIndex++;
                } else {
                    clearInterval(progressInterval);
                    
                    // Check actual status
                    try {
                        const response = await fetch(
                            `${API_BASE_URL}/step5/video-status/${jobId}?sessionId=${sessionId}`,
                            {
                                headers: {
                                    'Authorization': `Bearer ${authToken}`
                                }
                            }
                        );

                        const data = await response.json();
                        
                        if (data.status === 'completed') {
                            loading.classList.remove('active');
                            videoComplete.style.display = 'block';
                            
                            // Update preview
                            const videoPreview = document.getElementById('video-preview');
                            videoPreview.innerHTML = `
                                <video width="100%" height="100%" controls poster="${data.thumbnailUrl || ''}">
                                    <source src="${data.videoUrl || '#'}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            `;
                            
                            // Update status
                            document.getElementById('status-video').classList.add('completed');
                            showSuccess('Video generated successfully!');
                        } else {
                            // Continue polling
                            setTimeout(() => pollVideoGeneration(jobId, progressBar, progressText, loading, videoComplete), 5000);
                        }
                        
                    } catch (error) {
                        console.error('Video status error:', error);
                        clearInterval(progressInterval);
                        loading.classList.remove('active');
                        showError('Failed to generate video. Please try again.');
                    }
                }
            }, 2000);
        }

        function nextStep() {
            if (currentStep < 5) {
                currentStep++;
                updateStep();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStep();
            }
        }

        function updateStep() {
            // Hide all step contents
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Show current step content
            const currentContent = document.querySelector(`.step-content[data-step="${currentStep}"]`);
            if (currentContent) {
                currentContent.classList.add('active');
            }
            
            // Update progress steps
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active');
                if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
            
            updateProgressLine();
        }

        function updateProgressLine() {
            const progressLine = document.querySelector('.progress-line');
            if (progressLine) {
                const progress = ((currentStep - 1) / 4) * 100;
                progressLine.style.width = progress + '%';
            }
        }

        function updateProgressFromServer(data) {
            // Update progress based on server state
            if (data.stepsCompleted) {
                data.stepsCompleted.forEach(step => {
                    const stepNumber = getStepNumber(step);
                    const statusElement = document.getElementById(`status-${getStatusId(step)}`);
                    if (statusElement) {
                        statusElement.classList.add('completed');
                    }
                });
            }
        }

        function getStatusId(stepName) {
            const statusMap = {
                'upload_selfie': 'selfie',
                'customize_look': 'customization',
                'add_voice': 'voice',
                'type_message': 'script',
                'create_video': 'video'
            };
            return statusMap[stepName] || stepName;
        }

        // Helper functions for prompts and scripts
        function addPromptText(text) {
            const textarea = document.getElementById('customization-prompt');
            if (textarea) {
                textarea.value += text;
                textarea.focus();
            }
        }

        function setPrompt(prompt) {
            const textarea = document.getElementById('customization-prompt');
            if (textarea) {
                textarea.value = prompt;
            }
        }

        function setScript(script) {
            const textarea = document.getElementById('message-script');
            if (textarea) {
                textarea.value = script;
                updateCharCount();
                checkScriptStep();
            }
        }

        async function startOver() {
            if (confirm('Are you sure you want to start over? This will reset all your progress.')) {
                try {
                    // Reset session on server
                    await fetch(`${API_BASE_URL}/session/${sessionId}/reset`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                    
                    // Reset local state
                    currentStep = 1;
                    uploadedSelfie = null;
                    customizedImage = null;
                    recordedAudio = null;
                    messageScript = '';
                    
                    // Reset all status indicators
                    document.querySelectorAll('.status-icon').forEach(icon => {
                        icon.classList.remove('completed', 'active');
                    });
                    
                    // Reset forms
                    const customizationPrompt = document.getElementById('customization-prompt');
                    const messageScriptEl = document.getElementById('message-script');
                    
                    if (customizationPrompt) customizationPrompt.value = '';
                    if (messageScriptEl) messageScriptEl.value = '';
                    
                    // Reset upload area
                    const uploadArea = document.getElementById('selfie-upload');
                    if (uploadArea) {
                        uploadArea.classList.remove('has-file');
                        uploadArea.innerHTML = `
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h3>Drop your selfie here or click to upload</h3>
                            <p>Supports JPG, PNG, WebP up to 10MB</p>
                        `;
                    }
                    
                    updateStep();
                    showSuccess('Session reset successfully!');
                    
                } catch (error) {
                    console.error('Reset error:', error);
                    showError('Failed to reset session.');
                }
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });
    </script>script').addEventListener('input', checkScriptStep);

            // Video generation
            document.getElementById('generate-video').addEventListener('click', generateVideo);
        }

        function handleSelfieUpload(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedSelfie = e.target.result;
                
                // Update upload area
                const uploadArea = document.getElementById('selfie-upload');
                const preview = document.getElementById('selfie-preview');
                
                uploadArea.classList.add('has-file');
                uploadArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-check-circle" style="color: #28a745;"></i>
                    </div>
                    <h3>Selfie uploaded successfully!</h3>
                    <p>${file.name}</p>
                `;
                
                preview.src = uploadedSelfie;
                preview.style.display = 'block';
                
                // Update preview panel
                const previewAvatar = document.getElementById('preview-avatar');
                previewAvatar.innerHTML = `<img src="${uploadedSelfie}" alt="Uploaded selfie" />`;
                
                // Update status
                document.getElementById('status-selfie').classList.add('completed');
                document.getElementById('next-step-1').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        function applyCustomization() {
            const prompt = document.getElementById('customization-prompt').value;
            if (!prompt.trim()) {
                alert('Please describe the changes you want to make.');
                return;
            }

            const btn = document.getElementById('apply-customization');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Applying Changes...';

            // Simulate AI processing
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-magic"></i> Apply Customization';
                
                // Update status
                document.getElementById('status-customization').classList.add('completed');
                document.getElementById('next-step-2').disabled = false;
                
                // Show success message
                alert('Customization applied successfully!');
            }, 3000);
        }

        function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        function startRecording() {
            isRecording = true;
            recordingTime = 0;
            
            const recordBtn = document.getElementById('record-btn');
            const recordingStatus = document.getElementById('recording-status');
            
            recordBtn.classList.add('recording');
            recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
            recordingStatus.style.display = 'block';
            
            // Start timer
            recordingTimer = setInterval(() => {
                recordingTime++;
                const minutes = Math.floor(recordingTime / 60);
                const seconds = recordingTime % 60;
                document.getElementById('timer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Auto-stop after 30 seconds
                if (recordingTime >= 30) {
                    stopRecording();
                }
            }, 1000);

            // Simulate recording (in real app, use MediaRecorder API)
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    // Recording logic would go here
                    console.log('Recording started');
                })
                .catch(err => {
                    console.error('Error accessing microphone:', err);
                    stopRecording();
                });
        }

        function stopRecording() {
            isRecording = false;
            clearInterval(recordingTimer);
            
            const recordBtn = document.getElementById('record-btn');
            const recordingStatus = document.getElementById('recording-status');
            const audioControls = document.getElementById('audio-controls');
            
            recordBtn.classList.remove('recording');
            recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            recordingStatus.style.display = 'none';
            audioControls.style.display = 'flex';
            
            // Update status
            document.getElementById('status-voice').classList.add('completed');
            document.getElementById('next-step-3').disabled = false;
            
            recordedAudio = 'mock-audio-data'; // In real app, this would be actual audio data
        }

        function playRecording() {
            // In real app, play the recorded audio
            alert('Playing recorded audio...');
        }

        function reRecord() {
            document.getElementById('audio-controls').style.display = 'none';
            document.getElementById('status-voice').classList.remove('completed');
            document.getElementById('next-step-3').disabled = true;
            recordedAudio = null;
        }

        function updateCharCount() {
            const textarea = document.getElementById('message-script');
            const charCount = document.getElementById('char-count');
            const count = textarea.value.length;
            charCount.textContent = count;
            
            // Color coding for character count
            if (count < 50) {
                charCount.style.color = '#ff6b6b';
            } else if (count <= 500) {
                charCount.style.color = '#28a745';
            } else {
                charCount.style.color = '#ffc107';
            }
        }

        function checkScriptStep() {
            const script = document.getElementById('message-script').value;
            const nextBtn = document.getElementById('next-step-4');
            
            if (script.trim().length >= 10) {
                document.getElementById('status-script').classList.add('completed');
                nextBtn.disabled = false;
                messageScript = script;
            } else {
                document.getElementById('status-script').classList.remove('completed');
                nextBtn.disabled = true;
            }
        }

        function generateVideo() {
            const generateBtn = document.getElementById('generate-video');
            const loading = document.getElementById('video-generation-loading');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const videoComplete = document.getElementById('video-complete');
            
            generateBtn.style.display = 'none';
            loading.classList.add('active');
            
            // Simulate video generation process
            const steps = [
                { progress: 10, text: 'Processing selfie...' },
                { progress: 25, text: 'Applying customizations...' },
                { progress: 40, text: 'Analyzing voice patterns...' },
                { progress: 60, text: 'Generating lip sync...' },
                { progress: 80, text: 'Rendering video...' },
                { progress: 100, text: 'Finalizing...' }
            ];
            
            let stepIndex = 0;
            const progressInterval = setInterval(() => {
                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];
                    progressBar.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    stepIndex++;
                } else {
                    clearInterval(progressInterval);
                    
                    // Complete the process
                    setTimeout(() => {
                        loading.classList.remove('active');
                        videoComplete.style.display = 'block';
                        
                        // Update preview
                        document.getElementById('video-preview').innerHTML = `
                            <video width="100%" height="100%" controls>
                                <source src="#" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        `;
                        
                        // Update status
                        document.getElementById('status-video').classList.add('completed');
                    }, 1000);
                }
            }, 500);
        }

        function nextStep() {
            if (currentStep < 5) {
                currentStep++;
                updateStep();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStep();
            }
        }

        function updateStep() {
            // Hide all step contents
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Show current step content
            document.querySelector(`.step-content[data-step="${currentStep}"]`).classList.add('active');
            
            // Update progress steps
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active');
                if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
            
            updateProgressLine();
        }

        function updateProgressLine() {
            const progressLine = document.querySelector('.progress-line');
            const progress = ((currentStep - 1) / 4) * 100;
            progressLine.style.width = progress + '%';
        }

        function addPromptText(text) {
            const textarea = document.getElementById('customization-prompt');
            textarea.value += text;
            textarea.focus();
        }

        function setPrompt(prompt) {
            document.getElementById('customization-prompt').value = prompt;
        }

        function setScript(script) {
            document.getElementById('message-script').value = script;
            updateCharCount();
            checkScriptStep();
        }

        function startOver() {
            if (confirm('Are you sure you want to start over? This will reset all your progress.')) {
                currentStep = 1;
                uploadedSelfie = null;
                customizedImage = null;
                recordedAudio = null;
                messageScript = '';
                
                // Reset all status indicators
                document.querySelectorAll('.status-icon').forEach(icon => {
                    icon.classList.remove('completed', 'active');
                });
                
                // Reset forms
                document.getElementById('customization-prompt').value = '';
                document.getElementById('message-script').value = '';
                
                // Reset upload area
                document.getElementById('selfie-upload').classList.remove('has-file');
                document.getElementById('selfie-upload').innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3>Drop your selfie here or click to upload</h3>
                    <p>Supports JPG, PNG, WebP up to 10MB</p>
                `;
                
                updateStep();
            }
        }

        // Add event listeners for next buttons
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('next-step-1').addEventListener('click', nextStep);
            document.getElementById('next-step-2').addEventListener('click', nextStep);
            document.getElementById('next-step-3').addEventListener('click', nextStep);
            document.getElementById('next-step-4').addEventListener('click', nextStep);
        });
    </script>
</body>
</html>