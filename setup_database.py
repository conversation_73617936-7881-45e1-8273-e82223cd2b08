#!/usr/bin/env python3
"""
Database setup and management script for the Social Media Lead Generation Bot.

This script helps with database initialization, testing connections, and basic management tasks.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data_collection.database import init_db, test_db_connection, engine
from data_collection.models import Base
from config.settings import Config
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_connection():
    """Test the database connection."""
    logger.info("Testing database connection...")
    
    if test_db_connection():
        logger.info("✅ Database connection successful!")
        
        # Print connection details (without password)
        db_host = Config.database.DB_HOST
        db_port = Config.database.DB_PORT
        db_name = Config.database.DB_NAME
        db_user = Config.database.DB_USER

        logger.info(f"Connected to: {db_user}@{db_host}:{db_port}/{db_name}")
        return True
    else:
        logger.error("❌ Database connection failed!")
        logger.error("Please check your PostgreSQL configuration:")
        logger.error("1. Make sure PostgreSQL is running")
        logger.error("2. Check your .env file for correct connection parameters")
        logger.error("3. Verify the database exists")
        return False

def create_database():
    """Initialize database tables."""
    logger.info("Creating database tables...")
    
    try:
        init_db()
        logger.info("✅ Database tables created successfully!")
        
        # List created tables
        inspector = engine.inspect(engine)
        tables = inspector.get_table_names()
        logger.info(f"Created tables: {', '.join(tables)}")
        
    except Exception as e:
        logger.error(f"❌ Failed to create database tables: {e}")
        return False
    
    return True

def drop_tables():
    """Drop all database tables."""
    logger.warning("⚠️  This will drop ALL tables and data!")
    confirm = input("Are you sure you want to continue? (yes/no): ")
    
    if confirm.lower() != 'yes':
        logger.info("Operation cancelled.")
        return
    
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("✅ All tables dropped successfully!")
    except Exception as e:
        logger.error(f"❌ Failed to drop tables: {e}")

def reset_database():
    """Drop and recreate all database tables."""
    logger.info("Resetting database...")
    drop_tables()
    create_database()

def show_connection_info():
    """Display current database connection configuration."""
    logger.info("Current database configuration:")
    logger.info(f"Host: {Config.database.DB_HOST}")
    logger.info(f"Port: {Config.database.DB_PORT}")
    logger.info(f"Database: {Config.database.DB_NAME}")
    logger.info(f"User: {Config.database.DB_USER}")
    logger.info(f"Password: {'*' * len(Config.database.DB_PASSWORD)}")
    logger.info(f"Full URL: {Config.database.DATABASE_URL}")

def check_environment():
    """Check environment variables."""
    logger.info("Checking environment variables...")

    # Use the Config class to validate settings
    missing_settings = Config.validate_required_settings()

    if missing_settings:
        logger.error(f"Missing required settings: {', '.join(missing_settings)}")
        return False

    # Print configuration summary
    Config.print_config_summary()
    return True

def main():
    """Main function with command-line interface."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database setup and management')
    parser.add_argument(
        'action',
        choices=['test', 'create', 'drop', 'reset', 'info', 'check-env'],
        help='Action to perform'
    )
    
    args = parser.parse_args()
    
    if args.action == 'test':
        test_connection()
    elif args.action == 'create':
        if test_connection():
            create_database()
    elif args.action == 'drop':
        if test_connection():
            drop_tables()
    elif args.action == 'reset':
        if test_connection():
            reset_database()
    elif args.action == 'info':
        show_connection_info()
    elif args.action == 'check-env':
        check_environment()

if __name__ == "__main__":
    main()
